{"ast": null, "code": "import React,{useState,useEffect}from'react';import Layout from'../Layout/Layout';import{apiService}from'../../api';import{LoadingSpinner,ErrorMessage,EmptyState}from'../common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const KeyValueList=_ref=>{let{data,maxItems=10}=_ref;if(!data||typeof data!=='object')return/*#__PURE__*/_jsx(\"span\",{children:\"N/A\"});const entries=Object.entries(data).slice(0,maxItems);return/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs max-w-xs\",children:[entries.map(_ref2=>{let[key,value]=_ref2;const displayValue=typeof value==='string'&&value.length>30?value.slice(0,30)+'...':String(value);return/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsxs(\"strong\",{className:\"pr-1 text-gray-700\",children:[key,\":\"]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900 break-all\",children:displayValue})]},key);}),Object.entries(data).length>maxItems&&/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-500 italic\",children:\"...more\"})]});};const parseJSONSafe=text=>{if(!text)return null;if(typeof text==='object')return text;try{let parsed=JSON.parse(text);if(typeof parsed==='string')parsed=JSON.parse(parsed);return parsed;}catch{return null;}};const renderDetails=detailsData=>{const parsed=parseJSONSafe(detailsData);if(!parsed)return/*#__PURE__*/_jsx(\"span\",{children:typeof detailsData==='string'?detailsData:'N/A'});return/*#__PURE__*/_jsx(\"div\",{title:JSON.stringify(parsed,null,2),className:\"cursor-help\",children:/*#__PURE__*/_jsx(KeyValueList,{data:parsed})});};const renderPayload=payloadData=>{const parsed=parseJSONSafe(payloadData);if(!parsed)return/*#__PURE__*/_jsx(\"span\",{children:typeof payloadData==='string'?payloadData:'N/A'});return/*#__PURE__*/_jsx(\"div\",{title:JSON.stringify(parsed,null,2),className:\"cursor-help\",children:/*#__PURE__*/_jsx(KeyValueList,{data:parsed})});};const EditModal=_ref3=>{let{transaction,isOpen,onClose,onSave}=_ref3;const[payloadText,setPayloadText]=useState('');useEffect(()=>{if(transaction){if(transaction.payload===null||transaction.payload===undefined){setPayloadText('');}else if(typeof transaction.payload==='string'){setPayloadText(transaction.payload);}else{setPayloadText(JSON.stringify(transaction.payload,null,2));}}},[transaction]);if(!isOpen||!transaction)return null;const handleChange=e=>{setPayloadText(e.target.value);};const handleSave=()=>{try{let parsed;try{parsed=JSON.parse(payloadText);}catch{parsed=payloadText;}onSave(parsed);}catch{alert('Invalid JSON format. Please fix before saving.');}};return/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50\",onClick:onClose,role:\"dialog\",\"aria-modal\":\"true\",\"aria-labelledby\":\"edit-modal-title\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg w-11/12 md:w-2/3 max-w-3xl p-6 relative\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"h2\",{id:\"edit-modal-title\",className:\"text-xl font-semibold mb-4\",children:[\"Edit Transaction ID: \",transaction.id]}),/*#__PURE__*/_jsx(\"label\",{className:\"block font-medium mb-1\",htmlFor:\"payloadTextarea\",children:\"Payload (JSON or text)\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"payloadTextarea\",rows:10,className:\"w-full border border-gray-300 rounded p-2 font-mono text-sm resize-y\",value:payloadText,onChange:handleChange,spellCheck:false}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end mt-4 space-x-4\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"px-4 py-2 bg-gray-300 rounded hover:bg-gray-400 transition\",onClick:onClose,children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition\",onClick:handleSave,children:\"Save\"})]})]})});};const AllTransactions=()=>{const[transactions,setTransactions]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState('');const[sourceFilter,setSourceFilter]=useState('');const[statusFilter,setStatusFilter]=useState('');const[availableSources,setAvailableSources]=useState([]);const[availableStatuses,setAvailableStatuses]=useState(['success','pending','failed']);const[currentPage,setCurrentPage]=useState(1);const[pageSize,setPageSize]=useState(10);const[isModalOpen,setIsModalOpen]=useState(false);const[selectedTransaction,setSelectedTransaction]=useState(null);const fetchUsers=async()=>{try{setLoading(true);setError('');const data=await apiService.getDetails();setTransactions(data);const sources=Array.from(new Set(data.map(item=>item.source)));setAvailableSources(sources);const statuses=Array.from(new Set(data.map(item=>{var _item$status;return(_item$status=item.status)===null||_item$status===void 0?void 0:_item$status.toLowerCase();})));setAvailableStatuses(statuses.length>0?statuses:['success','pending','failed']);setCurrentPage(1);}catch(err){setError('Failed to load transactions. Please try again.');}finally{setLoading(false);}};const fetchFilteredData=async(source,status)=>{try{setLoading(true);setError('');const response=await apiService.getFilteredDetails(source===''?undefined:source,status===''?undefined:status);setTransactions(response!==null&&response!==void 0?response:[]);setCurrentPage(1);}catch(err){setError('Failed to filter data.');}finally{setLoading(false);}};useEffect(()=>{fetchUsers();},[]);const handleSourceChange=e=>{const val=e.target.value;setSourceFilter(val);fetchFilteredData(val,statusFilter);};const handleStatusChange=e=>{const val=e.target.value;setStatusFilter(val);fetchFilteredData(sourceFilter,val);};const handleRetry=()=>{fetchUsers();setSourceFilter('');setStatusFilter('');};const totalPages=Math.ceil(transactions.length/pageSize);const indexOfLast=currentPage*pageSize;const indexOfFirst=indexOfLast-pageSize;const currentTransactions=transactions.slice(indexOfFirst,indexOfLast);const openModal=transaction=>{setSelectedTransaction(transaction);setIsModalOpen(true);};const closeModal=()=>{setSelectedTransaction(null);setIsModalOpen(false);};const saveUpdatedDetails=async updatedPayload=>{if(!selectedTransaction)return;const payloadToSend=updatedPayload;const updatedTransactionData={...selectedTransaction,payload:payloadToSend};try{await apiService.updateTransaction(selectedTransaction.id,updatedTransactionData);setTransactions(prev=>prev.map(t=>t.id===selectedTransaction.id?{...t,payload:payloadToSend}:t));alert('Transaction updated successfully');closeModal();fetchUsers();}catch(error){alert('Failed to update transaction, please try again.');}};const statusBadgeClasses=status=>{switch(status.toLowerCase()){case'success':return'bg-green-100 text-green-800';case'pending':return'bg-yellow-100 text-yellow-800';case'failed':return'bg-red-100 text-red-800';default:return'bg-gray-100 text-gray-800';}};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900\",children:\"All Transactions\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mt-1\",children:\"Manage and monitor all transaction records\"})]}),!loading&&/*#__PURE__*/_jsxs(\"button\",{onClick:handleRetry,className:\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\",type:\"button\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sync-alt text-sm mr-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Refresh Data\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg border border-gray-200 p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-filter text-gray-400\"}),/*#__PURE__*/_jsx(\"label\",{className:\"text-sm font-semibold text-gray-700\",children:\"Source:\"}),/*#__PURE__*/_jsxs(\"select\",{value:sourceFilter,onChange:handleSourceChange,className:\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[120px]\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Sources\"}),availableSources.map(src=>/*#__PURE__*/_jsx(\"option\",{value:src,className:\"capitalize\",children:src},src))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-sm font-semibold text-gray-700\",children:\"Status:\"}),/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:handleStatusChange,className:\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[120px]\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Statuses\"}),availableStatuses.map(status=>/*#__PURE__*/_jsx(\"option\",{value:status,className:\"capitalize\",children:status},status))]})]})]})}),transactions.length>0&&!loading&&!error&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg border border-gray-200 p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-700\",children:[\"Showing \",/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:indexOfFirst+1}),\" to\",' ',/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:Math.min(indexOfLast,transactions.length)}),\" of\",' ',/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:transactions.length}),\" results\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-sm font-medium text-gray-700\",htmlFor:\"pageSize\",children:\"Show:\"}),/*#__PURE__*/_jsx(\"select\",{id:\"pageSize\",value:pageSize,onChange:e=>{setPageSize(Number(e.target.value));setCurrentPage(1);},className:\"border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",children:[10,20,40,50].map(size=>/*#__PURE__*/_jsxs(\"option\",{value:size,children:[size,\" per page\"]},size))})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",children:loading?/*#__PURE__*/_jsx(LoadingSpinner,{size:\"lg\",message:\"Loading transactions...\",className:\"py-16\"}):error?/*#__PURE__*/_jsx(ErrorMessage,{message:error,onRetry:handleRetry}):currentTransactions.length===0?/*#__PURE__*/_jsx(EmptyState,{title:\"No transactions found\",description:\"Try adjusting your filters or refresh the data\"}):/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-blue-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\",children:\"SNO\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\",children:\"Email\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\",children:\"Source\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\",children:\"Details\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\",children:\"Payload\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\",children:\"Reason\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\",children:\"Action\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-100\",children:currentTransactions.map((item,index)=>{var _item$details,_item$payload;const isFailed=item.status.toLowerCase()==='failed';const fullDetailsStr=(()=>{const d=parseJSONSafe(item.details);return d?JSON.stringify(d,null,2):typeof item.details==='string'?item.details:'N/A';})();const fullPayloadStr=(()=>{const p=parseJSONSafe(item.payload);return p?JSON.stringify(p,null,2):typeof item.payload==='string'?item.payload:'N/A';})();return/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-blue-50 transition-colors\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm text-gray-800\",children:index+1}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm text-gray-800\",children:/*#__PURE__*/_jsx(\"a\",{href:`mailto:${item.email}`,className:\"text-blue-600 hover:underline\",children:item.email})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm text-gray-800\",children:item.source}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm text-gray-800 max-w-xs whitespace-normal cursor-help\",title:fullDetailsStr,children:renderDetails((_item$details=item.details)!==null&&_item$details!==void 0?_item$details:'N/A')}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm text-gray-800 max-w-xs whitespace-normal cursor-help\",title:fullPayloadStr,children:renderPayload((_item$payload=item.payload)!==null&&_item$payload!==void 0?_item$payload:'N/A')}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm text-gray-800\",children:item.reason||'N/A'}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm\",children:/*#__PURE__*/_jsx(\"span\",{className:`\n                              px-2 py-1 rounded-full text-xs font-semibold capitalize\n                              ${statusBadgeClasses(item.status)}\n                            `,children:item.status})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm\",children:isFailed?/*#__PURE__*/_jsxs(\"button\",{className:\"text-indigo-600 hover:underline flex items-center space-x-1 whitespace-nowrap\",\"aria-label\":\"Edit and Sync transaction\",onClick:()=>openModal(item),type:\"button\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-edit\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sync-alt\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Edit & Sync\"})]}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-400 italic\",children:\"\\u2014\"})})]},item.id);})})]})})}),transactions.length>0&&!loading&&!error&&currentTransactions.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-t border-gray-200 px-6 py-4 rounded-b-xl\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(1),disabled:currentPage===1,className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\"aria-label\":\"First Page\",type:\"button\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-angle-double-left\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(Math.max(currentPage-1,1)),disabled:currentPage===1,className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\"aria-label\":\"Previous Page\",type:\"button\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-angle-left\"})}),(()=>{const pages=[];const startPage=Math.max(1,currentPage-2);const endPage=Math.min(totalPages,currentPage+2);if(startPage>1){pages.push(/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(1),className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors\",children:\"1\"},1));if(startPage>2){pages.push(/*#__PURE__*/_jsx(\"span\",{className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300\",children:\"...\"},\"ellipsis1\"));}}for(let i=startPage;i<=endPage;i++){pages.push(/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(i),className:`px-3 py-2 text-sm font-medium border-t border-b border-gray-300 transition-colors ${i===currentPage?'text-blue-600 bg-blue-50 border-blue-500':'text-gray-500 bg-white hover:bg-gray-50 hover:text-gray-700'}`,children:i},i));}if(endPage<totalPages){if(endPage<totalPages-1){pages.push(/*#__PURE__*/_jsx(\"span\",{className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300\",children:\"...\"},\"ellipsis2\"));}pages.push(/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(totalPages),className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors\",children:totalPages},totalPages));}return pages;})(),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(Math.min(currentPage+1,totalPages)),disabled:currentPage===totalPages,className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\"aria-label\":\"Next Page\",type:\"button\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-angle-right\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(totalPages),disabled:currentPage===totalPages,className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\"aria-label\":\"Last Page\",type:\"button\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-angle-double-right\"})})]})})}),/*#__PURE__*/_jsx(EditModal,{transaction:selectedTransaction,isOpen:isModalOpen,onClose:closeModal,onSave:saveUpdatedDetails})]})});};export default AllTransactions;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "apiService", "LoadingSpinner", "ErrorMessage", "EmptyState", "jsx", "_jsx", "jsxs", "_jsxs", "KeyValueList", "_ref", "data", "maxItems", "children", "entries", "Object", "slice", "className", "map", "_ref2", "key", "value", "displayValue", "length", "String", "parseJSONSafe", "text", "parsed", "JSON", "parse", "renderDetails", "detailsData", "title", "stringify", "renderPayload", "payloadData", "EditModal", "_ref3", "transaction", "isOpen", "onClose", "onSave", "payloadText", "setPayloadText", "payload", "undefined", "handleChange", "e", "target", "handleSave", "alert", "onClick", "role", "stopPropagation", "id", "htmlFor", "rows", "onChange", "spell<PERSON>heck", "type", "AllTransactions", "transactions", "setTransactions", "loading", "setLoading", "error", "setError", "sourceFilter", "setSourceFilter", "statusFilter", "setStatus<PERSON>ilter", "availableSources", "setAvailableSources", "availableStatuses", "setAvailableStatuses", "currentPage", "setCurrentPage", "pageSize", "setPageSize", "isModalOpen", "setIsModalOpen", "selectedTransaction", "setSelectedTransaction", "fetchUsers", "getDetails", "sources", "Array", "from", "Set", "item", "source", "statuses", "_item$status", "status", "toLowerCase", "err", "fetchFilteredData", "response", "getFilteredDetails", "handleSourceChange", "val", "handleStatusChange", "handleRetry", "totalPages", "Math", "ceil", "indexOfLast", "indexOfFirst", "currentTransactions", "openModal", "closeModal", "saveUpdatedDetails", "updatedPayload", "payloadToSend", "updatedTransactionData", "updateTransaction", "prev", "t", "statusBadgeClasses", "src", "min", "Number", "size", "message", "onRetry", "description", "index", "_item$details", "_item$payload", "isFailed", "fullDetailsStr", "d", "details", "fullPayloadStr", "p", "href", "email", "reason", "disabled", "max", "pages", "startPage", "endPage", "push", "i"], "sources": ["D:/ELGI/src/components/AllTransactions/AllTransactions.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Layout from '../Layout/Layout';\nimport { apiService } from '../../api';\nimport { LoadingSpinner, ErrorMessage, EmptyState } from '../common';\n\ninterface Transaction {\n  id: number;\n  email: string;\n  details: any;\n  payload: any;\n  status: string;\n  reason: string | null;\n  source: string;\n  created_at: string;\n}\n\ninterface EditModalProps {\n  transaction: Transaction | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (updatedPayload: any) => void;\n}\n\nconst KeyValueList: React.FC<{ data: any; maxItems?: number }> = ({ data, maxItems = 10 }) => {\n  if (!data || typeof data !== 'object') return <span>N/A</span>;\n  const entries = Object.entries(data).slice(0, maxItems);\n  return (\n    <div className=\"text-xs max-w-xs\">\n      {entries.map(([key, value]) => {\n        const displayValue =\n          typeof value === 'string' && value.length > 30\n            ? value.slice(0, 30) + '...'\n            : String(value);\n        return (\n          <div key={key} className=\"flex justify-between\">\n            <strong className=\"pr-1 text-gray-700\">{key}:</strong>\n            <span className=\"text-gray-900 break-all\">{displayValue}</span>\n          </div>\n        );\n      })}\n      {Object.entries(data).length > maxItems && (\n        <div className=\"text-gray-500 italic\">...more</div>\n      )}\n    </div>\n  );\n};\n\nconst parseJSONSafe = (text: any): any => {\n  if (!text) return null;\n  if (typeof text === 'object') return text;\n  try {\n    let parsed = JSON.parse(text);\n    if (typeof parsed === 'string') parsed = JSON.parse(parsed);\n    return parsed;\n  } catch {\n    return null;\n  }\n};\n\nconst renderDetails = (detailsData: any) => {\n  const parsed = parseJSONSafe(detailsData);\n  if (!parsed) return <span>{typeof detailsData === 'string' ? detailsData : 'N/A'}</span>;\n  return (\n    <div title={JSON.stringify(parsed, null, 2)} className=\"cursor-help\">\n      <KeyValueList data={parsed} />\n    </div>\n  );\n};\n\nconst renderPayload = (payloadData: any) => {\n  const parsed = parseJSONSafe(payloadData);\n  if (!parsed) return <span>{typeof payloadData === 'string' ? payloadData : 'N/A'}</span>;\n  return (\n    <div title={JSON.stringify(parsed, null, 2)} className=\"cursor-help\">\n      <KeyValueList data={parsed} />\n    </div>\n  );\n};\n\nconst EditModal: React.FC<EditModalProps> = ({ transaction, isOpen, onClose, onSave }) => {\n  const [payloadText, setPayloadText] = useState<string>('');\n\n  useEffect(() => {\n    if (transaction) {\n      if (transaction.payload === null || transaction.payload === undefined) {\n        setPayloadText('');\n      } else if (typeof transaction.payload === 'string') {\n        setPayloadText(transaction.payload);\n      } else {\n        setPayloadText(JSON.stringify(transaction.payload, null, 2));\n      }\n    }\n  }, [transaction]);\n\n  if (!isOpen || !transaction) return null;\n\n  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    setPayloadText(e.target.value);\n  };\n\n  const handleSave = () => {\n    try {\n      let parsed;\n      try {\n        parsed = JSON.parse(payloadText);\n      } catch {\n        parsed = payloadText;\n      }\n      onSave(parsed);\n    } catch {\n      alert('Invalid JSON format. Please fix before saving.');\n    }\n  };\n\n  return (\n    <div\n      className=\"fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50\"\n      onClick={onClose}\n      role=\"dialog\"\n      aria-modal=\"true\"\n      aria-labelledby=\"edit-modal-title\"\n    >\n      <div\n        className=\"bg-white rounded-lg w-11/12 md:w-2/3 max-w-3xl p-6 relative\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        <h2 id=\"edit-modal-title\" className=\"text-xl font-semibold mb-4\">\n          Edit Transaction ID: {transaction.id}\n        </h2>\n        <label className=\"block font-medium mb-1\" htmlFor=\"payloadTextarea\">\n          Payload (JSON or text)\n        </label>\n        <textarea\n          id=\"payloadTextarea\"\n          rows={10}\n          className=\"w-full border border-gray-300 rounded p-2 font-mono text-sm resize-y\"\n          value={payloadText}\n          onChange={handleChange}\n          spellCheck={false}\n        />\n        <div className=\"flex justify-end mt-4 space-x-4\">\n          <button\n            type=\"button\"\n            className=\"px-4 py-2 bg-gray-300 rounded hover:bg-gray-400 transition\"\n            onClick={onClose}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"button\"\n            className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition\"\n            onClick={handleSave}\n          >\n            Save\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n\nconst AllTransactions: React.FC = () => {\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [sourceFilter, setSourceFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [availableSources, setAvailableSources] = useState<string[]>([]);\n  const [availableStatuses, setAvailableStatuses] = useState<string[]>(['success', 'pending', 'failed']);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const data: Transaction[] = await apiService.getDetails();\n      setTransactions(data);\n\n      const sources = Array.from(new Set(data.map((item) => item.source)));\n      setAvailableSources(sources);\n\n      const statuses = Array.from(new Set(data.map((item) => item.status?.toLowerCase())));\n      setAvailableStatuses(statuses.length > 0 ? statuses : ['success', 'pending', 'failed']);\n\n      setCurrentPage(1);\n    } catch (err) {\n      setError('Failed to load transactions. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchFilteredData = async (source: string, status: string) => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await apiService.getFilteredDetails(\n        source === '' ? undefined : source,\n        status === '' ? undefined : status\n      );\n      setTransactions(response ?? []);\n      setCurrentPage(1);\n    } catch (err) {\n      setError('Failed to filter data.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n\n  const handleSourceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const val = e.target.value;\n    setSourceFilter(val);\n    fetchFilteredData(val, statusFilter);\n  };\n\n  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const val = e.target.value;\n    setStatusFilter(val);\n    fetchFilteredData(sourceFilter, val);\n  };\n\n  const handleRetry = () => {\n    fetchUsers();\n    setSourceFilter('');\n    setStatusFilter('');\n  };\n\n  const totalPages = Math.ceil(transactions.length / pageSize);\n  const indexOfLast = currentPage * pageSize;\n  const indexOfFirst = indexOfLast - pageSize;\n  const currentTransactions = transactions.slice(indexOfFirst, indexOfLast);\n\n  const openModal = (transaction: Transaction) => {\n    setSelectedTransaction(transaction);\n    setIsModalOpen(true);\n  };\n\n  const closeModal = () => {\n    setSelectedTransaction(null);\n    setIsModalOpen(false);\n  };\n\n  const saveUpdatedDetails = async (updatedPayload: any) => {\n    if (!selectedTransaction) return;\n    const payloadToSend = updatedPayload;\n    const updatedTransactionData = {\n      ...selectedTransaction,\n      payload: payloadToSend,\n    };\n    try {\n      await apiService.updateTransaction(selectedTransaction.id, updatedTransactionData);\n      setTransactions((prev) =>\n        prev.map((t) =>\n          t.id === selectedTransaction.id ? { ...t, payload: payloadToSend } : t\n        )\n      );\n      alert('Transaction updated successfully');\n      closeModal();\n      fetchUsers();\n    } catch (error) {\n      alert('Failed to update transaction, please try again.');\n    }\n  };\n\n  const statusBadgeClasses = (status: string) => {\n    switch (status.toLowerCase()) {\n      case 'success': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'failed': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <Layout>\n      <div className=\"space-y-6\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">All Transactions</h1>\n            <p className=\"text-gray-600 mt-1\">Manage and monitor all transaction records</p>\n          </div>\n          {!loading && (\n            <button\n              onClick={handleRetry}\n              className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\"\n              type=\"button\"\n            >\n              <i className=\"fas fa-sync-alt text-sm mr-2\" />\n              <span>Refresh Data</span>\n            </button>\n          )}\n        </div>\n\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-6\">\n            <div className=\"flex items-center space-x-3\">\n              <i className=\"fas fa-filter text-gray-400\"></i>\n              <label className=\"text-sm font-semibold text-gray-700\">Source:</label>\n              <select\n                value={sourceFilter}\n                onChange={handleSourceChange}\n                className=\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[120px]\"\n              >\n                <option value=\"\">All Sources</option>\n                {availableSources.map((src) => (\n                  <option key={src} value={src} className=\"capitalize\">{src}</option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"flex items-center space-x-3\">\n              <label className=\"text-sm font-semibold text-gray-700\">Status:</label>\n              <select\n                value={statusFilter}\n                onChange={handleStatusChange}\n                className=\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[120px]\"\n              >\n                <option value=\"\">All Statuses</option>\n                {availableStatuses.map((status) => (\n                  <option key={status} value={status} className=\"capitalize\">{status}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Page size dropdown at the top */}\n        {transactions.length > 0 && !loading && !error && (\n          <div className=\"bg-white rounded-lg border border-gray-200 p-4 mb-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"text-sm text-gray-700\">\n                Showing <span className=\"font-medium\">{indexOfFirst + 1}</span> to{' '}\n                <span className=\"font-medium\">{Math.min(indexOfLast, transactions.length)}</span> of{' '}\n                <span className=\"font-medium\">{transactions.length}</span> results\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <label className=\"text-sm font-medium text-gray-700\" htmlFor=\"pageSize\">\n                  Show:\n                </label>\n                <select\n                  id=\"pageSize\"\n                  value={pageSize}\n                  onChange={(e) => {\n                    setPageSize(Number(e.target.value));\n                    setCurrentPage(1);\n                  }}\n                  className=\"border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  {[10, 20, 40, 50].map((size) => (\n                    <option key={size} value={size}>{size} per page</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n          {loading ? (\n            <LoadingSpinner size=\"lg\" message=\"Loading transactions...\" className=\"py-16\" />\n          ) : error ? (\n            <ErrorMessage message={error} onRetry={handleRetry} />\n          ) : currentTransactions.length === 0 ? (\n            <EmptyState\n              title=\"No transactions found\"\n              description=\"Try adjusting your filters or refresh the data\"\n            />\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-blue-50\">\n                  <tr>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\">SNO</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\">Email</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\">Source</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\">Details</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\">Payload</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\">Reason</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\">Status</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider\">Action</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-100\">\n                  {currentTransactions.map((item,index) => {\n                    const isFailed = item.status.toLowerCase() === 'failed';\n                    const fullDetailsStr = (() => {\n                      const d = parseJSONSafe(item.details);\n                      return d ? JSON.stringify(d, null, 2) : (typeof item.details === 'string' ? item.details : 'N/A');\n                    })();\n                    const fullPayloadStr = (() => {\n                      const p = parseJSONSafe(item.payload);\n                      return p ? JSON.stringify(p, null, 2) : (typeof item.payload === 'string' ? item.payload : 'N/A');\n                    })();\n                    return (\n                      <tr key={item.id} className=\"hover:bg-blue-50 transition-colors\">\n                        <td className=\"px-6 py-4 text-sm text-gray-800\">{index+1}</td>\n                        <td className=\"px-6 py-4 text-sm text-gray-800\">\n                          <a href={`mailto:${item.email}`} className=\"text-blue-600 hover:underline\">\n                            {item.email}\n                          </a>\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-800\">{item.source}</td>\n                        <td\n                          className=\"px-6 py-4 text-sm text-gray-800 max-w-xs whitespace-normal cursor-help\"\n                          title={fullDetailsStr}\n                        >\n                          {renderDetails(item.details ?? 'N/A')}\n                        </td>\n                        <td\n                          className=\"px-6 py-4 text-sm text-gray-800 max-w-xs whitespace-normal cursor-help\"\n                          title={fullPayloadStr}\n                        >\n                          {renderPayload(item.payload ?? 'N/A')}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-800\">{item.reason || 'N/A'}</td>\n                        <td className=\"px-6 py-4 text-sm\">\n                          <span\n                            className={`\n                              px-2 py-1 rounded-full text-xs font-semibold capitalize\n                              ${statusBadgeClasses(item.status)}\n                            `}\n                          >\n                            {item.status}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 text-sm\">\n                          {isFailed ? (\n                            <button\n                              className=\"text-indigo-600 hover:underline flex items-center space-x-1 whitespace-nowrap\"\n                              aria-label=\"Edit and Sync transaction\"\n                              onClick={() => openModal(item)}\n                              type=\"button\"\n                            >\n                              <i className=\"fas fa-edit\" />\n                              <i className=\"fas fa-sync-alt\" />\n                              <span>Edit &amp; Sync</span>\n                            </button>\n                          ) : (\n                            <span className=\"text-gray-400 italic\">—</span>\n                          )}\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n\n        {/* Pagination controls at the bottom */}\n        {transactions.length > 0 && !loading && !error && currentTransactions.length > 0 && (\n          <div className=\"bg-white border-t border-gray-200 px-6 py-4 rounded-b-xl\">\n            <div className=\"flex items-center justify-center\">\n              <div className=\"flex items-center space-x-1\">\n                <button\n                  onClick={() => setCurrentPage(1)}\n                  disabled={currentPage === 1}\n                  className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  aria-label=\"First Page\"\n                  type=\"button\"\n                >\n                  <i className=\"fas fa-angle-double-left\"></i>\n                </button>\n\n                <button\n                  onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}\n                  disabled={currentPage === 1}\n                  className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  aria-label=\"Previous Page\"\n                  type=\"button\"\n                >\n                  <i className=\"fas fa-angle-left\"></i>\n                </button>\n\n                {/* Page numbers */}\n                {(() => {\n                  const pages = [];\n                  const startPage = Math.max(1, currentPage - 2);\n                  const endPage = Math.min(totalPages, currentPage + 2);\n\n                  if (startPage > 1) {\n                    pages.push(\n                      <button\n                        key={1}\n                        onClick={() => setCurrentPage(1)}\n                        className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors\"\n                      >\n                        1\n                      </button>\n                    );\n                    if (startPage > 2) {\n                      pages.push(\n                        <span key=\"ellipsis1\" className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300\">\n                          ...\n                        </span>\n                      );\n                    }\n                  }\n\n                  for (let i = startPage; i <= endPage; i++) {\n                    pages.push(\n                      <button\n                        key={i}\n                        onClick={() => setCurrentPage(i)}\n                        className={`px-3 py-2 text-sm font-medium border-t border-b border-gray-300 transition-colors ${\n                          i === currentPage\n                            ? 'text-blue-600 bg-blue-50 border-blue-500'\n                            : 'text-gray-500 bg-white hover:bg-gray-50 hover:text-gray-700'\n                        }`}\n                      >\n                        {i}\n                      </button>\n                    );\n                  }\n\n                  if (endPage < totalPages) {\n                    if (endPage < totalPages - 1) {\n                      pages.push(\n                        <span key=\"ellipsis2\" className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300\">\n                          ...\n                        </span>\n                      );\n                    }\n                    pages.push(\n                      <button\n                        key={totalPages}\n                        onClick={() => setCurrentPage(totalPages)}\n                        className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors\"\n                      >\n                        {totalPages}\n                      </button>\n                    );\n                  }\n\n                  return pages;\n                })()}\n\n                <button\n                  onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}\n                  disabled={currentPage === totalPages}\n                  className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  aria-label=\"Next Page\"\n                  type=\"button\"\n                >\n                  <i className=\"fas fa-angle-right\"></i>\n                </button>\n\n                <button\n                  onClick={() => setCurrentPage(totalPages)}\n                  disabled={currentPage === totalPages}\n                  className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  aria-label=\"Last Page\"\n                  type=\"button\"\n                >\n                  <i className=\"fas fa-angle-double-right\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        <EditModal\n          transaction={selectedTransaction}\n          isOpen={isModalOpen}\n          onClose={closeModal}\n          onSave={saveUpdatedDetails}\n        />\n      </div>\n    </Layout>\n  );\n};\n\nexport default AllTransactions;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,kBAAkB,CACrC,OAASC,UAAU,KAAQ,WAAW,CACtC,OAASC,cAAc,CAAEC,YAAY,CAAEC,UAAU,KAAQ,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAoBrE,KAAM,CAAAC,YAAwD,CAAGC,IAAA,EAA6B,IAA5B,CAAEC,IAAI,CAAEC,QAAQ,CAAG,EAAG,CAAC,CAAAF,IAAA,CACvF,GAAI,CAACC,IAAI,EAAI,MAAO,CAAAA,IAAI,GAAK,QAAQ,CAAE,mBAAOL,IAAA,SAAAO,QAAA,CAAM,KAAG,CAAM,CAAC,CAC9D,KAAM,CAAAC,OAAO,CAAGC,MAAM,CAACD,OAAO,CAACH,IAAI,CAAC,CAACK,KAAK,CAAC,CAAC,CAAEJ,QAAQ,CAAC,CACvD,mBACEJ,KAAA,QAAKS,SAAS,CAAC,kBAAkB,CAAAJ,QAAA,EAC9BC,OAAO,CAACI,GAAG,CAACC,KAAA,EAAkB,IAAjB,CAACC,GAAG,CAAEC,KAAK,CAAC,CAAAF,KAAA,CACxB,KAAM,CAAAG,YAAY,CAChB,MAAO,CAAAD,KAAK,GAAK,QAAQ,EAAIA,KAAK,CAACE,MAAM,CAAG,EAAE,CAC1CF,KAAK,CAACL,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAC1BQ,MAAM,CAACH,KAAK,CAAC,CACnB,mBACEb,KAAA,QAAeS,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,eAC7CL,KAAA,WAAQS,SAAS,CAAC,oBAAoB,CAAAJ,QAAA,EAAEO,GAAG,CAAC,GAAC,EAAQ,CAAC,cACtDd,IAAA,SAAMW,SAAS,CAAC,yBAAyB,CAAAJ,QAAA,CAAES,YAAY,CAAO,CAAC,GAFvDF,GAGL,CAAC,CAEV,CAAC,CAAC,CACDL,MAAM,CAACD,OAAO,CAACH,IAAI,CAAC,CAACY,MAAM,CAAGX,QAAQ,eACrCN,IAAA,QAAKW,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,SAAO,CAAK,CACnD,EACE,CAAC,CAEV,CAAC,CAED,KAAM,CAAAY,aAAa,CAAIC,IAAS,EAAU,CACxC,GAAI,CAACA,IAAI,CAAE,MAAO,KAAI,CACtB,GAAI,MAAO,CAAAA,IAAI,GAAK,QAAQ,CAAE,MAAO,CAAAA,IAAI,CACzC,GAAI,CACF,GAAI,CAAAC,MAAM,CAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAC7B,GAAI,MAAO,CAAAC,MAAM,GAAK,QAAQ,CAAEA,MAAM,CAAGC,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC,CAC3D,MAAO,CAAAA,MAAM,CACf,CAAE,KAAM,CACN,MAAO,KAAI,CACb,CACF,CAAC,CAED,KAAM,CAAAG,aAAa,CAAIC,WAAgB,EAAK,CAC1C,KAAM,CAAAJ,MAAM,CAAGF,aAAa,CAACM,WAAW,CAAC,CACzC,GAAI,CAACJ,MAAM,CAAE,mBAAOrB,IAAA,SAAAO,QAAA,CAAO,MAAO,CAAAkB,WAAW,GAAK,QAAQ,CAAGA,WAAW,CAAG,KAAK,CAAO,CAAC,CACxF,mBACEzB,IAAA,QAAK0B,KAAK,CAAEJ,IAAI,CAACK,SAAS,CAACN,MAAM,CAAE,IAAI,CAAE,CAAC,CAAE,CAACV,SAAS,CAAC,aAAa,CAAAJ,QAAA,cAClEP,IAAA,CAACG,YAAY,EAACE,IAAI,CAAEgB,MAAO,CAAE,CAAC,CAC3B,CAAC,CAEV,CAAC,CAED,KAAM,CAAAO,aAAa,CAAIC,WAAgB,EAAK,CAC1C,KAAM,CAAAR,MAAM,CAAGF,aAAa,CAACU,WAAW,CAAC,CACzC,GAAI,CAACR,MAAM,CAAE,mBAAOrB,IAAA,SAAAO,QAAA,CAAO,MAAO,CAAAsB,WAAW,GAAK,QAAQ,CAAGA,WAAW,CAAG,KAAK,CAAO,CAAC,CACxF,mBACE7B,IAAA,QAAK0B,KAAK,CAAEJ,IAAI,CAACK,SAAS,CAACN,MAAM,CAAE,IAAI,CAAE,CAAC,CAAE,CAACV,SAAS,CAAC,aAAa,CAAAJ,QAAA,cAClEP,IAAA,CAACG,YAAY,EAACE,IAAI,CAAEgB,MAAO,CAAE,CAAC,CAC3B,CAAC,CAEV,CAAC,CAED,KAAM,CAAAS,SAAmC,CAAGC,KAAA,EAA8C,IAA7C,CAAEC,WAAW,CAAEC,MAAM,CAAEC,OAAO,CAAEC,MAAO,CAAC,CAAAJ,KAAA,CACnF,KAAM,CAACK,WAAW,CAAEC,cAAc,CAAC,CAAG7C,QAAQ,CAAS,EAAE,CAAC,CAE1DC,SAAS,CAAC,IAAM,CACd,GAAIuC,WAAW,CAAE,CACf,GAAIA,WAAW,CAACM,OAAO,GAAK,IAAI,EAAIN,WAAW,CAACM,OAAO,GAAKC,SAAS,CAAE,CACrEF,cAAc,CAAC,EAAE,CAAC,CACpB,CAAC,IAAM,IAAI,MAAO,CAAAL,WAAW,CAACM,OAAO,GAAK,QAAQ,CAAE,CAClDD,cAAc,CAACL,WAAW,CAACM,OAAO,CAAC,CACrC,CAAC,IAAM,CACLD,cAAc,CAACf,IAAI,CAACK,SAAS,CAACK,WAAW,CAACM,OAAO,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAC9D,CACF,CACF,CAAC,CAAE,CAACN,WAAW,CAAC,CAAC,CAEjB,GAAI,CAACC,MAAM,EAAI,CAACD,WAAW,CAAE,MAAO,KAAI,CAExC,KAAM,CAAAQ,YAAY,CAAIC,CAAyC,EAAK,CAClEJ,cAAc,CAACI,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAC,CAChC,CAAC,CAED,KAAM,CAAA4B,UAAU,CAAGA,CAAA,GAAM,CACvB,GAAI,CACF,GAAI,CAAAtB,MAAM,CACV,GAAI,CACFA,MAAM,CAAGC,IAAI,CAACC,KAAK,CAACa,WAAW,CAAC,CAClC,CAAE,KAAM,CACNf,MAAM,CAAGe,WAAW,CACtB,CACAD,MAAM,CAACd,MAAM,CAAC,CAChB,CAAE,KAAM,CACNuB,KAAK,CAAC,gDAAgD,CAAC,CACzD,CACF,CAAC,CAED,mBACE5C,IAAA,QACEW,SAAS,CAAC,4EAA4E,CACtFkC,OAAO,CAAEX,OAAQ,CACjBY,IAAI,CAAC,QAAQ,CACb,aAAW,MAAM,CACjB,kBAAgB,kBAAkB,CAAAvC,QAAA,cAElCL,KAAA,QACES,SAAS,CAAC,6DAA6D,CACvEkC,OAAO,CAAGJ,CAAC,EAAKA,CAAC,CAACM,eAAe,CAAC,CAAE,CAAAxC,QAAA,eAEpCL,KAAA,OAAI8C,EAAE,CAAC,kBAAkB,CAACrC,SAAS,CAAC,4BAA4B,CAAAJ,QAAA,EAAC,uBAC1C,CAACyB,WAAW,CAACgB,EAAE,EAClC,CAAC,cACLhD,IAAA,UAAOW,SAAS,CAAC,wBAAwB,CAACsC,OAAO,CAAC,iBAAiB,CAAA1C,QAAA,CAAC,wBAEpE,CAAO,CAAC,cACRP,IAAA,aACEgD,EAAE,CAAC,iBAAiB,CACpBE,IAAI,CAAE,EAAG,CACTvC,SAAS,CAAC,sEAAsE,CAChFI,KAAK,CAAEqB,WAAY,CACnBe,QAAQ,CAAEX,YAAa,CACvBY,UAAU,CAAE,KAAM,CACnB,CAAC,cACFlD,KAAA,QAAKS,SAAS,CAAC,iCAAiC,CAAAJ,QAAA,eAC9CP,IAAA,WACEqD,IAAI,CAAC,QAAQ,CACb1C,SAAS,CAAC,4DAA4D,CACtEkC,OAAO,CAAEX,OAAQ,CAAA3B,QAAA,CAClB,QAED,CAAQ,CAAC,cACTP,IAAA,WACEqD,IAAI,CAAC,QAAQ,CACb1C,SAAS,CAAC,uEAAuE,CACjFkC,OAAO,CAAEF,UAAW,CAAApC,QAAA,CACrB,MAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAGD,KAAM,CAAA+C,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGhE,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAACiE,OAAO,CAAEC,UAAU,CAAC,CAAGlE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACmE,KAAK,CAAEC,QAAQ,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACuE,YAAY,CAAEC,eAAe,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1E,QAAQ,CAAW,EAAE,CAAC,CACtE,KAAM,CAAC2E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5E,QAAQ,CAAW,CAAC,SAAS,CAAE,SAAS,CAAE,QAAQ,CAAC,CAAC,CACtG,KAAM,CAAC6E,WAAW,CAAEC,cAAc,CAAC,CAAG9E,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC+E,QAAQ,CAAEC,WAAW,CAAC,CAAGhF,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiF,WAAW,CAAEC,cAAc,CAAC,CAAGlF,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACmF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGpF,QAAQ,CAAqB,IAAI,CAAC,CAExF,KAAM,CAAAqF,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACFnB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZ,KAAM,CAAAvD,IAAmB,CAAG,KAAM,CAAAV,UAAU,CAACmF,UAAU,CAAC,CAAC,CACzDtB,eAAe,CAACnD,IAAI,CAAC,CAErB,KAAM,CAAA0E,OAAO,CAAGC,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAAC7E,IAAI,CAACO,GAAG,CAAEuE,IAAI,EAAKA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CACpElB,mBAAmB,CAACa,OAAO,CAAC,CAE5B,KAAM,CAAAM,QAAQ,CAAGL,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAAC7E,IAAI,CAACO,GAAG,CAAEuE,IAAI,OAAAG,YAAA,QAAAA,YAAA,CAAKH,IAAI,CAACI,MAAM,UAAAD,YAAA,iBAAXA,YAAA,CAAaE,WAAW,CAAC,CAAC,GAAC,CAAC,CAAC,CACpFpB,oBAAoB,CAACiB,QAAQ,CAACpE,MAAM,CAAG,CAAC,CAAGoE,QAAQ,CAAG,CAAC,SAAS,CAAE,SAAS,CAAE,QAAQ,CAAC,CAAC,CAEvFf,cAAc,CAAC,CAAC,CAAC,CACnB,CAAE,MAAOmB,GAAG,CAAE,CACZ7B,QAAQ,CAAC,gDAAgD,CAAC,CAC5D,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAgC,iBAAiB,CAAG,KAAAA,CAAON,MAAc,CAAEG,MAAc,GAAK,CAClE,GAAI,CACF7B,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZ,KAAM,CAAA+B,QAAQ,CAAG,KAAM,CAAAhG,UAAU,CAACiG,kBAAkB,CAClDR,MAAM,GAAK,EAAE,CAAG7C,SAAS,CAAG6C,MAAM,CAClCG,MAAM,GAAK,EAAE,CAAGhD,SAAS,CAAGgD,MAC9B,CAAC,CACD/B,eAAe,CAACmC,QAAQ,SAARA,QAAQ,UAARA,QAAQ,CAAI,EAAE,CAAC,CAC/BrB,cAAc,CAAC,CAAC,CAAC,CACnB,CAAE,MAAOmB,GAAG,CAAE,CACZ7B,QAAQ,CAAC,wBAAwB,CAAC,CACpC,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDjE,SAAS,CAAC,IAAM,CACdoF,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAgB,kBAAkB,CAAIpD,CAAuC,EAAK,CACtE,KAAM,CAAAqD,GAAG,CAAGrD,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAC1B+C,eAAe,CAACgC,GAAG,CAAC,CACpBJ,iBAAiB,CAACI,GAAG,CAAE/B,YAAY,CAAC,CACtC,CAAC,CAED,KAAM,CAAAgC,kBAAkB,CAAItD,CAAuC,EAAK,CACtE,KAAM,CAAAqD,GAAG,CAAGrD,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAC1BiD,eAAe,CAAC8B,GAAG,CAAC,CACpBJ,iBAAiB,CAAC7B,YAAY,CAAEiC,GAAG,CAAC,CACtC,CAAC,CAED,KAAM,CAAAE,WAAW,CAAGA,CAAA,GAAM,CACxBnB,UAAU,CAAC,CAAC,CACZf,eAAe,CAAC,EAAE,CAAC,CACnBE,eAAe,CAAC,EAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAAiC,UAAU,CAAGC,IAAI,CAACC,IAAI,CAAC5C,YAAY,CAACtC,MAAM,CAAGsD,QAAQ,CAAC,CAC5D,KAAM,CAAA6B,WAAW,CAAG/B,WAAW,CAAGE,QAAQ,CAC1C,KAAM,CAAA8B,YAAY,CAAGD,WAAW,CAAG7B,QAAQ,CAC3C,KAAM,CAAA+B,mBAAmB,CAAG/C,YAAY,CAAC7C,KAAK,CAAC2F,YAAY,CAAED,WAAW,CAAC,CAEzE,KAAM,CAAAG,SAAS,CAAIvE,WAAwB,EAAK,CAC9C4C,sBAAsB,CAAC5C,WAAW,CAAC,CACnC0C,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAA8B,UAAU,CAAGA,CAAA,GAAM,CACvB5B,sBAAsB,CAAC,IAAI,CAAC,CAC5BF,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAA+B,kBAAkB,CAAG,KAAO,CAAAC,cAAmB,EAAK,CACxD,GAAI,CAAC/B,mBAAmB,CAAE,OAC1B,KAAM,CAAAgC,aAAa,CAAGD,cAAc,CACpC,KAAM,CAAAE,sBAAsB,CAAG,CAC7B,GAAGjC,mBAAmB,CACtBrC,OAAO,CAAEqE,aACX,CAAC,CACD,GAAI,CACF,KAAM,CAAAhH,UAAU,CAACkH,iBAAiB,CAAClC,mBAAmB,CAAC3B,EAAE,CAAE4D,sBAAsB,CAAC,CAClFpD,eAAe,CAAEsD,IAAI,EACnBA,IAAI,CAAClG,GAAG,CAAEmG,CAAC,EACTA,CAAC,CAAC/D,EAAE,GAAK2B,mBAAmB,CAAC3B,EAAE,CAAG,CAAE,GAAG+D,CAAC,CAAEzE,OAAO,CAAEqE,aAAc,CAAC,CAAGI,CACvE,CACF,CAAC,CACDnE,KAAK,CAAC,kCAAkC,CAAC,CACzC4D,UAAU,CAAC,CAAC,CACZ3B,UAAU,CAAC,CAAC,CACd,CAAE,MAAOlB,KAAK,CAAE,CACdf,KAAK,CAAC,iDAAiD,CAAC,CAC1D,CACF,CAAC,CAED,KAAM,CAAAoE,kBAAkB,CAAIzB,MAAc,EAAK,CAC7C,OAAQA,MAAM,CAACC,WAAW,CAAC,CAAC,EAC1B,IAAK,SAAS,CAAE,MAAO,6BAA6B,CACpD,IAAK,SAAS,CAAE,MAAO,+BAA+B,CACtD,IAAK,QAAQ,CAAE,MAAO,yBAAyB,CAC/C,QAAS,MAAO,2BAA2B,CAC7C,CACF,CAAC,CAED,mBACExF,IAAA,CAACN,MAAM,EAAAa,QAAA,cACLL,KAAA,QAAKS,SAAS,CAAC,WAAW,CAAAJ,QAAA,eACxBL,KAAA,QAAKS,SAAS,CAAC,qFAAqF,CAAAJ,QAAA,eAClGL,KAAA,QAAAK,QAAA,eACEP,IAAA,OAAIW,SAAS,CAAC,kCAAkC,CAAAJ,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACtEP,IAAA,MAAGW,SAAS,CAAC,oBAAoB,CAAAJ,QAAA,CAAC,4CAA0C,CAAG,CAAC,EAC7E,CAAC,CACL,CAACkD,OAAO,eACPvD,KAAA,WACE2C,OAAO,CAAEmD,WAAY,CACrBrF,SAAS,CAAC,oHAAoH,CAC9H0C,IAAI,CAAC,QAAQ,CAAA9C,QAAA,eAEbP,IAAA,MAAGW,SAAS,CAAC,8BAA8B,CAAE,CAAC,cAC9CX,IAAA,SAAAO,QAAA,CAAM,cAAY,CAAM,CAAC,EACnB,CACT,EACE,CAAC,cAENP,IAAA,QAAKW,SAAS,CAAC,gDAAgD,CAAAJ,QAAA,cAC7DL,KAAA,QAAKS,SAAS,CAAC,+EAA+E,CAAAJ,QAAA,eAC5FL,KAAA,QAAKS,SAAS,CAAC,6BAA6B,CAAAJ,QAAA,eAC1CP,IAAA,MAAGW,SAAS,CAAC,6BAA6B,CAAI,CAAC,cAC/CX,IAAA,UAAOW,SAAS,CAAC,qCAAqC,CAAAJ,QAAA,CAAC,SAAO,CAAO,CAAC,cACtEL,KAAA,WACEa,KAAK,CAAE8C,YAAa,CACpBV,QAAQ,CAAE0C,kBAAmB,CAC7BlF,SAAS,CAAC,gJAAgJ,CAAAJ,QAAA,eAE1JP,IAAA,WAAQe,KAAK,CAAC,EAAE,CAAAR,QAAA,CAAC,aAAW,CAAQ,CAAC,CACpC0D,gBAAgB,CAACrD,GAAG,CAAEqG,GAAG,eACxBjH,IAAA,WAAkBe,KAAK,CAAEkG,GAAI,CAACtG,SAAS,CAAC,YAAY,CAAAJ,QAAA,CAAE0G,GAAG,EAA5CA,GAAqD,CACnE,CAAC,EACI,CAAC,EACN,CAAC,cAEN/G,KAAA,QAAKS,SAAS,CAAC,6BAA6B,CAAAJ,QAAA,eAC1CP,IAAA,UAAOW,SAAS,CAAC,qCAAqC,CAAAJ,QAAA,CAAC,SAAO,CAAO,CAAC,cACtEL,KAAA,WACEa,KAAK,CAAEgD,YAAa,CACpBZ,QAAQ,CAAE4C,kBAAmB,CAC7BpF,SAAS,CAAC,gJAAgJ,CAAAJ,QAAA,eAE1JP,IAAA,WAAQe,KAAK,CAAC,EAAE,CAAAR,QAAA,CAAC,cAAY,CAAQ,CAAC,CACrC4D,iBAAiB,CAACvD,GAAG,CAAE2E,MAAM,eAC5BvF,IAAA,WAAqBe,KAAK,CAAEwE,MAAO,CAAC5E,SAAS,CAAC,YAAY,CAAAJ,QAAA,CAAEgF,MAAM,EAArDA,MAA8D,CAC5E,CAAC,EACI,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CAGLhC,YAAY,CAACtC,MAAM,CAAG,CAAC,EAAI,CAACwC,OAAO,EAAI,CAACE,KAAK,eAC5C3D,IAAA,QAAKW,SAAS,CAAC,qDAAqD,CAAAJ,QAAA,cAClEL,KAAA,QAAKS,SAAS,CAAC,mCAAmC,CAAAJ,QAAA,eAChDL,KAAA,QAAKS,SAAS,CAAC,uBAAuB,CAAAJ,QAAA,EAAC,UAC7B,cAAAP,IAAA,SAAMW,SAAS,CAAC,aAAa,CAAAJ,QAAA,CAAE8F,YAAY,CAAG,CAAC,CAAO,CAAC,MAAG,CAAC,GAAG,cACtErG,IAAA,SAAMW,SAAS,CAAC,aAAa,CAAAJ,QAAA,CAAE2F,IAAI,CAACgB,GAAG,CAACd,WAAW,CAAE7C,YAAY,CAACtC,MAAM,CAAC,CAAO,CAAC,MAAG,CAAC,GAAG,cACxFjB,IAAA,SAAMW,SAAS,CAAC,aAAa,CAAAJ,QAAA,CAAEgD,YAAY,CAACtC,MAAM,CAAO,CAAC,WAC5D,EAAK,CAAC,cACNf,KAAA,QAAKS,SAAS,CAAC,6BAA6B,CAAAJ,QAAA,eAC1CP,IAAA,UAAOW,SAAS,CAAC,mCAAmC,CAACsC,OAAO,CAAC,UAAU,CAAA1C,QAAA,CAAC,OAExE,CAAO,CAAC,cACRP,IAAA,WACEgD,EAAE,CAAC,UAAU,CACbjC,KAAK,CAAEwD,QAAS,CAChBpB,QAAQ,CAAGV,CAAC,EAAK,CACf+B,WAAW,CAAC2C,MAAM,CAAC1E,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAC,CAAC,CACnCuD,cAAc,CAAC,CAAC,CAAC,CACnB,CAAE,CACF3D,SAAS,CAAC,kIAAkI,CAAAJ,QAAA,CAE3I,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAACK,GAAG,CAAEwG,IAAI,eACzBlH,KAAA,WAAmBa,KAAK,CAAEqG,IAAK,CAAA7G,QAAA,EAAE6G,IAAI,CAAC,WAAS,GAAlCA,IAA0C,CACxD,CAAC,CACI,CAAC,EACN,CAAC,EACH,CAAC,CACH,CACN,cAEDpH,IAAA,QAAKW,SAAS,CAAC,sEAAsE,CAAAJ,QAAA,CAClFkD,OAAO,cACNzD,IAAA,CAACJ,cAAc,EAACwH,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,yBAAyB,CAAC1G,SAAS,CAAC,OAAO,CAAE,CAAC,CAC9EgD,KAAK,cACP3D,IAAA,CAACH,YAAY,EAACwH,OAAO,CAAE1D,KAAM,CAAC2D,OAAO,CAAEtB,WAAY,CAAE,CAAC,CACpDM,mBAAmB,CAACrF,MAAM,GAAK,CAAC,cAClCjB,IAAA,CAACF,UAAU,EACT4B,KAAK,CAAC,uBAAuB,CAC7B6F,WAAW,CAAC,gDAAgD,CAC7D,CAAC,cAEFvH,IAAA,QAAKW,SAAS,CAAC,iBAAiB,CAAAJ,QAAA,cAC9BL,KAAA,UAAOS,SAAS,CAAC,qCAAqC,CAAAJ,QAAA,eACpDP,IAAA,UAAOW,SAAS,CAAC,YAAY,CAAAJ,QAAA,cAC3BL,KAAA,OAAAK,QAAA,eACEP,IAAA,OAAIW,SAAS,CAAC,kFAAkF,CAAAJ,QAAA,CAAC,KAAG,CAAI,CAAC,cACzGP,IAAA,OAAIW,SAAS,CAAC,kFAAkF,CAAAJ,QAAA,CAAC,OAAK,CAAI,CAAC,cAC3GP,IAAA,OAAIW,SAAS,CAAC,kFAAkF,CAAAJ,QAAA,CAAC,QAAM,CAAI,CAAC,cAC5GP,IAAA,OAAIW,SAAS,CAAC,kFAAkF,CAAAJ,QAAA,CAAC,SAAO,CAAI,CAAC,cAC7GP,IAAA,OAAIW,SAAS,CAAC,kFAAkF,CAAAJ,QAAA,CAAC,SAAO,CAAI,CAAC,cAC7GP,IAAA,OAAIW,SAAS,CAAC,kFAAkF,CAAAJ,QAAA,CAAC,QAAM,CAAI,CAAC,cAC5GP,IAAA,OAAIW,SAAS,CAAC,kFAAkF,CAAAJ,QAAA,CAAC,QAAM,CAAI,CAAC,cAC5GP,IAAA,OAAIW,SAAS,CAAC,kFAAkF,CAAAJ,QAAA,CAAC,QAAM,CAAI,CAAC,EAC1G,CAAC,CACA,CAAC,cACRP,IAAA,UAAOW,SAAS,CAAC,mCAAmC,CAAAJ,QAAA,CACjD+F,mBAAmB,CAAC1F,GAAG,CAAC,CAACuE,IAAI,CAACqC,KAAK,GAAK,KAAAC,aAAA,CAAAC,aAAA,CACvC,KAAM,CAAAC,QAAQ,CAAGxC,IAAI,CAACI,MAAM,CAACC,WAAW,CAAC,CAAC,GAAK,QAAQ,CACvD,KAAM,CAAAoC,cAAc,CAAG,CAAC,IAAM,CAC5B,KAAM,CAAAC,CAAC,CAAG1G,aAAa,CAACgE,IAAI,CAAC2C,OAAO,CAAC,CACrC,MAAO,CAAAD,CAAC,CAAGvG,IAAI,CAACK,SAAS,CAACkG,CAAC,CAAE,IAAI,CAAE,CAAC,CAAC,CAAI,MAAO,CAAA1C,IAAI,CAAC2C,OAAO,GAAK,QAAQ,CAAG3C,IAAI,CAAC2C,OAAO,CAAG,KAAM,CACnG,CAAC,EAAE,CAAC,CACJ,KAAM,CAAAC,cAAc,CAAG,CAAC,IAAM,CAC5B,KAAM,CAAAC,CAAC,CAAG7G,aAAa,CAACgE,IAAI,CAAC7C,OAAO,CAAC,CACrC,MAAO,CAAA0F,CAAC,CAAG1G,IAAI,CAACK,SAAS,CAACqG,CAAC,CAAE,IAAI,CAAE,CAAC,CAAC,CAAI,MAAO,CAAA7C,IAAI,CAAC7C,OAAO,GAAK,QAAQ,CAAG6C,IAAI,CAAC7C,OAAO,CAAG,KAAM,CACnG,CAAC,EAAE,CAAC,CACJ,mBACEpC,KAAA,OAAkBS,SAAS,CAAC,oCAAoC,CAAAJ,QAAA,eAC9DP,IAAA,OAAIW,SAAS,CAAC,iCAAiC,CAAAJ,QAAA,CAAEiH,KAAK,CAAC,CAAC,CAAK,CAAC,cAC9DxH,IAAA,OAAIW,SAAS,CAAC,iCAAiC,CAAAJ,QAAA,cAC7CP,IAAA,MAAGiI,IAAI,CAAE,UAAU9C,IAAI,CAAC+C,KAAK,EAAG,CAACvH,SAAS,CAAC,+BAA+B,CAAAJ,QAAA,CACvE4E,IAAI,CAAC+C,KAAK,CACV,CAAC,CACF,CAAC,cACLlI,IAAA,OAAIW,SAAS,CAAC,iCAAiC,CAAAJ,QAAA,CAAE4E,IAAI,CAACC,MAAM,CAAK,CAAC,cAClEpF,IAAA,OACEW,SAAS,CAAC,wEAAwE,CAClFe,KAAK,CAAEkG,cAAe,CAAArH,QAAA,CAErBiB,aAAa,EAAAiG,aAAA,CAACtC,IAAI,CAAC2C,OAAO,UAAAL,aAAA,UAAAA,aAAA,CAAI,KAAK,CAAC,CACnC,CAAC,cACLzH,IAAA,OACEW,SAAS,CAAC,wEAAwE,CAClFe,KAAK,CAAEqG,cAAe,CAAAxH,QAAA,CAErBqB,aAAa,EAAA8F,aAAA,CAACvC,IAAI,CAAC7C,OAAO,UAAAoF,aAAA,UAAAA,aAAA,CAAI,KAAK,CAAC,CACnC,CAAC,cACL1H,IAAA,OAAIW,SAAS,CAAC,iCAAiC,CAAAJ,QAAA,CAAE4E,IAAI,CAACgD,MAAM,EAAI,KAAK,CAAK,CAAC,cAC3EnI,IAAA,OAAIW,SAAS,CAAC,mBAAmB,CAAAJ,QAAA,cAC/BP,IAAA,SACEW,SAAS,CAAE;AACvC;AACA,gCAAgCqG,kBAAkB,CAAC7B,IAAI,CAACI,MAAM,CAAC;AAC/D,6BAA8B,CAAAhF,QAAA,CAED4E,IAAI,CAACI,MAAM,CACR,CAAC,CACL,CAAC,cACLvF,IAAA,OAAIW,SAAS,CAAC,mBAAmB,CAAAJ,QAAA,CAC9BoH,QAAQ,cACPzH,KAAA,WACES,SAAS,CAAC,+EAA+E,CACzF,aAAW,2BAA2B,CACtCkC,OAAO,CAAEA,CAAA,GAAM0D,SAAS,CAACpB,IAAI,CAAE,CAC/B9B,IAAI,CAAC,QAAQ,CAAA9C,QAAA,eAEbP,IAAA,MAAGW,SAAS,CAAC,aAAa,CAAE,CAAC,cAC7BX,IAAA,MAAGW,SAAS,CAAC,iBAAiB,CAAE,CAAC,cACjCX,IAAA,SAAAO,QAAA,CAAM,aAAe,CAAM,CAAC,EACtB,CAAC,cAETP,IAAA,SAAMW,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,QAAC,CAAM,CAC/C,CACC,CAAC,GA9CE4E,IAAI,CAACnC,EA+CV,CAAC,CAET,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CACN,CACE,CAAC,CAGLO,YAAY,CAACtC,MAAM,CAAG,CAAC,EAAI,CAACwC,OAAO,EAAI,CAACE,KAAK,EAAI2C,mBAAmB,CAACrF,MAAM,CAAG,CAAC,eAC9EjB,IAAA,QAAKW,SAAS,CAAC,0DAA0D,CAAAJ,QAAA,cACvEP,IAAA,QAAKW,SAAS,CAAC,kCAAkC,CAAAJ,QAAA,cAC/CL,KAAA,QAAKS,SAAS,CAAC,6BAA6B,CAAAJ,QAAA,eAC1CP,IAAA,WACE6C,OAAO,CAAEA,CAAA,GAAMyB,cAAc,CAAC,CAAC,CAAE,CACjC8D,QAAQ,CAAE/D,WAAW,GAAK,CAAE,CAC5B1D,SAAS,CAAC,iMAAiM,CAC3M,aAAW,YAAY,CACvB0C,IAAI,CAAC,QAAQ,CAAA9C,QAAA,cAEbP,IAAA,MAAGW,SAAS,CAAC,0BAA0B,CAAI,CAAC,CACtC,CAAC,cAETX,IAAA,WACE6C,OAAO,CAAEA,CAAA,GAAMyB,cAAc,CAAC4B,IAAI,CAACmC,GAAG,CAAChE,WAAW,CAAG,CAAC,CAAE,CAAC,CAAC,CAAE,CAC5D+D,QAAQ,CAAE/D,WAAW,GAAK,CAAE,CAC5B1D,SAAS,CAAC,+LAA+L,CACzM,aAAW,eAAe,CAC1B0C,IAAI,CAAC,QAAQ,CAAA9C,QAAA,cAEbP,IAAA,MAAGW,SAAS,CAAC,mBAAmB,CAAI,CAAC,CAC/B,CAAC,CAGR,CAAC,IAAM,CACN,KAAM,CAAA2H,KAAK,CAAG,EAAE,CAChB,KAAM,CAAAC,SAAS,CAAGrC,IAAI,CAACmC,GAAG,CAAC,CAAC,CAAEhE,WAAW,CAAG,CAAC,CAAC,CAC9C,KAAM,CAAAmE,OAAO,CAAGtC,IAAI,CAACgB,GAAG,CAACjB,UAAU,CAAE5B,WAAW,CAAG,CAAC,CAAC,CAErD,GAAIkE,SAAS,CAAG,CAAC,CAAE,CACjBD,KAAK,CAACG,IAAI,cACRzI,IAAA,WAEE6C,OAAO,CAAEA,CAAA,GAAMyB,cAAc,CAAC,CAAC,CAAE,CACjC3D,SAAS,CAAC,+IAA+I,CAAAJ,QAAA,CAC1J,GAED,EALO,CAKC,CACV,CAAC,CACD,GAAIgI,SAAS,CAAG,CAAC,CAAE,CACjBD,KAAK,CAACG,IAAI,cACRzI,IAAA,SAAsBW,SAAS,CAAC,wFAAwF,CAAAJ,QAAA,CAAC,KAEzH,EAFU,WAEJ,CACR,CAAC,CACH,CACF,CAEA,IAAK,GAAI,CAAAmI,CAAC,CAAGH,SAAS,CAAEG,CAAC,EAAIF,OAAO,CAAEE,CAAC,EAAE,CAAE,CACzCJ,KAAK,CAACG,IAAI,cACRzI,IAAA,WAEE6C,OAAO,CAAEA,CAAA,GAAMyB,cAAc,CAACoE,CAAC,CAAE,CACjC/H,SAAS,CAAE,qFACT+H,CAAC,GAAKrE,WAAW,CACb,0CAA0C,CAC1C,6DAA6D,EAChE,CAAA9D,QAAA,CAEFmI,CAAC,EARGA,CASC,CACV,CAAC,CACH,CAEA,GAAIF,OAAO,CAAGvC,UAAU,CAAE,CACxB,GAAIuC,OAAO,CAAGvC,UAAU,CAAG,CAAC,CAAE,CAC5BqC,KAAK,CAACG,IAAI,cACRzI,IAAA,SAAsBW,SAAS,CAAC,wFAAwF,CAAAJ,QAAA,CAAC,KAEzH,EAFU,WAEJ,CACR,CAAC,CACH,CACA+H,KAAK,CAACG,IAAI,cACRzI,IAAA,WAEE6C,OAAO,CAAEA,CAAA,GAAMyB,cAAc,CAAC2B,UAAU,CAAE,CAC1CtF,SAAS,CAAC,+IAA+I,CAAAJ,QAAA,CAExJ0F,UAAU,EAJNA,UAKC,CACV,CAAC,CACH,CAEA,MAAO,CAAAqC,KAAK,CACd,CAAC,EAAE,CAAC,cAEJtI,IAAA,WACE6C,OAAO,CAAEA,CAAA,GAAMyB,cAAc,CAAC4B,IAAI,CAACgB,GAAG,CAAC7C,WAAW,CAAG,CAAC,CAAE4B,UAAU,CAAC,CAAE,CACrEmC,QAAQ,CAAE/D,WAAW,GAAK4B,UAAW,CACrCtF,SAAS,CAAC,+LAA+L,CACzM,aAAW,WAAW,CACtB0C,IAAI,CAAC,QAAQ,CAAA9C,QAAA,cAEbP,IAAA,MAAGW,SAAS,CAAC,oBAAoB,CAAI,CAAC,CAChC,CAAC,cAETX,IAAA,WACE6C,OAAO,CAAEA,CAAA,GAAMyB,cAAc,CAAC2B,UAAU,CAAE,CAC1CmC,QAAQ,CAAE/D,WAAW,GAAK4B,UAAW,CACrCtF,SAAS,CAAC,iMAAiM,CAC3M,aAAW,WAAW,CACtB0C,IAAI,CAAC,QAAQ,CAAA9C,QAAA,cAEbP,IAAA,MAAGW,SAAS,CAAC,2BAA2B,CAAI,CAAC,CACvC,CAAC,EACN,CAAC,CACH,CAAC,CACH,CACN,cAIDX,IAAA,CAAC8B,SAAS,EACRE,WAAW,CAAE2C,mBAAoB,CACjC1C,MAAM,CAAEwC,WAAY,CACpBvC,OAAO,CAAEsE,UAAW,CACpBrE,MAAM,CAAEsE,kBAAmB,CAC5B,CAAC,EACC,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAnD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}