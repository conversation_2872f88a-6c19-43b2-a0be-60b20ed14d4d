{"ast": null, "code": "import React,{useState}from'react';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Header=()=>{const{logout,user}=useAuth();const[showProfileDropdown,setShowProfileDropdown]=useState(false);const handleLogout=()=>{logout();setShowProfileDropdown(false);};return/*#__PURE__*/_jsx(\"header\",{className:\"bg-white border-b border-gray-200 px-6 py-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-end\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowProfileDropdown(!showProfileDropdown),className:\"flex items-center space-x-3 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full flex items-center justify-center\",style:{backgroundColor:'#3579F3'},children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user text-white text-sm\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-gray-900\",children:(user===null||user===void 0?void 0:user.username)||'Admin'}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chevron-down text-gray-400 text-xs\"})]}),showProfileDropdown&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 z-10\",onClick:()=>setShowProfileDropdown(false)}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-20\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:handleLogout,className:\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors rounded-lg\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sign-out-alt text-gray-400 text-sm\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-700\",children:\"Logout\"})]})})]})]})})});};export default Header;", "map": {"version": 3, "names": ["React", "useState", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Header", "logout", "user", "showProfileDropdown", "setShowProfileDropdown", "handleLogout", "className", "children", "onClick", "style", "backgroundColor", "username"], "sources": ["D:/ELGI/src/components/Layout/Header.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst Header: React.FC = () => {\n  const { logout, user } = useAuth();\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    setShowProfileDropdown(false);\n  };\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex justify-end\">\n        <div className=\"relative\">\n          <button\n            onClick={() => setShowProfileDropdown(!showProfileDropdown)}\n            className=\"flex items-center space-x-3 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <div className=\"w-8 h-8 rounded-full flex items-center justify-center\" style={{ backgroundColor: '#3579F3' }}>\n              <i className=\"fas fa-user text-white text-sm\"></i>\n            </div>\n            <span className=\"font-medium text-gray-900\">\n              {user?.username || 'Admin'}\n            </span>\n            <i className=\"fas fa-chevron-down text-gray-400 text-xs\"></i>\n          </button>\n\n          {showProfileDropdown && (\n            <>\n              {/* Backdrop */}\n              <div\n                className=\"fixed inset-0 z-10\"\n                onClick={() => setShowProfileDropdown(false)}\n              ></div>\n              \n              {/* Dropdown */}\n              <div className=\"absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-20\">\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors rounded-lg\"\n                >\n                  <i className=\"fas fa-sign-out-alt text-gray-400 text-sm\"></i>\n                  <span className=\"text-gray-700\">Logout</span>\n                </button>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,KAAM,CAAAC,MAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAEC,MAAM,CAAEC,IAAK,CAAC,CAAGT,OAAO,CAAC,CAAC,CAClC,KAAM,CAACU,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAErE,KAAM,CAAAa,YAAY,CAAGA,CAAA,GAAM,CACzBJ,MAAM,CAAC,CAAC,CACRG,sBAAsB,CAAC,KAAK,CAAC,CAC/B,CAAC,CAED,mBACET,IAAA,WAAQW,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC7DZ,IAAA,QAAKW,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BV,KAAA,QAAKS,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBV,KAAA,WACEW,OAAO,CAAEA,CAAA,GAAMJ,sBAAsB,CAAC,CAACD,mBAAmB,CAAE,CAC5DG,SAAS,CAAC,qFAAqF,CAAAC,QAAA,eAE/FZ,IAAA,QAAKW,SAAS,CAAC,uDAAuD,CAACG,KAAK,CAAE,CAAEC,eAAe,CAAE,SAAU,CAAE,CAAAH,QAAA,cAC3GZ,IAAA,MAAGW,SAAS,CAAC,gCAAgC,CAAI,CAAC,CAC/C,CAAC,cACNX,IAAA,SAAMW,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACxC,CAAAL,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,QAAQ,GAAI,OAAO,CACtB,CAAC,cACPhB,IAAA,MAAGW,SAAS,CAAC,2CAA2C,CAAI,CAAC,EACvD,CAAC,CAERH,mBAAmB,eAClBN,KAAA,CAAAE,SAAA,EAAAQ,QAAA,eAEEZ,IAAA,QACEW,SAAS,CAAC,oBAAoB,CAC9BE,OAAO,CAAEA,CAAA,GAAMJ,sBAAsB,CAAC,KAAK,CAAE,CACzC,CAAC,cAGPT,IAAA,QAAKW,SAAS,CAAC,sFAAsF,CAAAC,QAAA,cACnGV,KAAA,WACEW,OAAO,CAAEH,YAAa,CACtBC,SAAS,CAAC,sGAAsG,CAAAC,QAAA,eAEhHZ,IAAA,MAAGW,SAAS,CAAC,2CAA2C,CAAI,CAAC,cAC7DX,IAAA,SAAMW,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,EACvC,CAAC,CACN,CAAC,EACN,CACH,EACE,CAAC,CACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}