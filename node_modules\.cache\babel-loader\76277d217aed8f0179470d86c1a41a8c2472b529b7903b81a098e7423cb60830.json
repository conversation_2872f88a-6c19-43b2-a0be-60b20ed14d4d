{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Pagination=_ref=>{let{currentPage,totalPages,pageSize,totalItems,onPageChange,onPageSizeChange,pageSizeOptions=[10,20,40,50]}=_ref;const indexOfFirst=(currentPage-1)*pageSize;const indexOfLast=Math.min(currentPage*pageSize,totalItems);const renderPageNumbers=()=>{const pages=[];const startPage=Math.max(1,currentPage-2);const endPage=Math.min(totalPages,currentPage+2);if(startPage>1){pages.push(/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(1),className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors\",children:\"1\"},1));if(startPage>2){pages.push(/*#__PURE__*/_jsx(\"span\",{className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300\",children:\"...\"},\"ellipsis1\"));}}for(let i=startPage;i<=endPage;i++){pages.push(/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(i),className:`px-3 py-2 text-sm font-medium border-t border-b border-gray-300 transition-colors ${i===currentPage?'text-blue-600 bg-blue-50 border-blue-500':'text-gray-500 bg-white hover:bg-gray-50 hover:text-gray-700'}`,children:i},i));}if(endPage<totalPages){if(endPage<totalPages-1){pages.push(/*#__PURE__*/_jsx(\"span\",{className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300\",children:\"...\"},\"ellipsis2\"));}pages.push(/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(totalPages),className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors\",children:totalPages},totalPages));}return pages;};if(totalItems===0)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-t border-gray-200 px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-700\",children:[\"Showing \",/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:indexOfFirst+1}),\" to\",' ',/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:indexOfLast}),\" of\",' ',/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:totalItems}),\" results\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-sm font-medium text-gray-700\",htmlFor:\"pageSize\",children:\"Show:\"}),/*#__PURE__*/_jsx(\"select\",{id:\"pageSize\",value:pageSize,onChange:e=>onPageSizeChange(Number(e.target.value)),className:\"border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",children:pageSizeOptions.map(size=>/*#__PURE__*/_jsxs(\"option\",{value:size,children:[size,\" per page\"]},size))})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(1),disabled:currentPage===1,className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\"aria-label\":\"First Page\",type:\"button\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-angle-double-left\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(Math.max(currentPage-1,1)),disabled:currentPage===1,className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\"aria-label\":\"Previous Page\",type:\"button\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-angle-left\"})}),renderPageNumbers(),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(Math.min(currentPage+1,totalPages)),disabled:currentPage===totalPages,className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\"aria-label\":\"Next Page\",type:\"button\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-angle-right\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(totalPages),disabled:currentPage===totalPages,className:\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\"aria-label\":\"Last Page\",type:\"button\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-angle-double-right\"})})]})]})});};export default Pagination;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Pagination", "_ref", "currentPage", "totalPages", "pageSize", "totalItems", "onPageChange", "onPageSizeChange", "pageSizeOptions", "indexOfFirst", "indexOfLast", "Math", "min", "renderPageNumbers", "pages", "startPage", "max", "endPage", "push", "onClick", "className", "children", "i", "htmlFor", "id", "value", "onChange", "e", "Number", "target", "map", "size", "disabled", "type"], "sources": ["D:/ELGI/src/components/common/Pagination.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface PaginationProps {\n  currentPage: number;\n  totalPages: number;\n  pageSize: number;\n  totalItems: number;\n  onPageChange: (page: number) => void;\n  onPageSizeChange: (size: number) => void;\n  pageSizeOptions?: number[];\n}\n\nconst Pagination: React.FC<PaginationProps> = ({\n  currentPage,\n  totalPages,\n  pageSize,\n  totalItems,\n  onPageChange,\n  onPageSizeChange,\n  pageSizeOptions = [10, 20, 40, 50]\n}) => {\n  const indexOfFirst = (currentPage - 1) * pageSize;\n  const indexOfLast = Math.min(currentPage * pageSize, totalItems);\n\n  const renderPageNumbers = () => {\n    const pages = [];\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n\n    if (startPage > 1) {\n      pages.push(\n        <button\n          key={1}\n          onClick={() => onPageChange(1)}\n          className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors\"\n        >\n          1\n        </button>\n      );\n      if (startPage > 2) {\n        pages.push(\n          <span key=\"ellipsis1\" className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300\">\n            ...\n          </span>\n        );\n      }\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(\n        <button\n          key={i}\n          onClick={() => onPageChange(i)}\n          className={`px-3 py-2 text-sm font-medium border-t border-b border-gray-300 transition-colors ${\n            i === currentPage\n              ? 'text-blue-600 bg-blue-50 border-blue-500'\n              : 'text-gray-500 bg-white hover:bg-gray-50 hover:text-gray-700'\n          }`}\n        >\n          {i}\n        </button>\n      );\n    }\n\n    if (endPage < totalPages) {\n      if (endPage < totalPages - 1) {\n        pages.push(\n          <span key=\"ellipsis2\" className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300\">\n            ...\n          </span>\n        );\n      }\n      pages.push(\n        <button\n          key={totalPages}\n          onClick={() => onPageChange(totalPages)}\n          className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors\"\n        >\n          {totalPages}\n        </button>\n      );\n    }\n\n    return pages;\n  };\n\n  if (totalItems === 0) return null;\n\n  return (\n    <div className=\"bg-white border-t border-gray-200 px-6 py-4\">\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"text-sm text-gray-700\">\n            Showing <span className=\"font-medium\">{indexOfFirst + 1}</span> to{' '}\n            <span className=\"font-medium\">{indexOfLast}</span> of{' '}\n            <span className=\"font-medium\">{totalItems}</span> results\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <label className=\"text-sm font-medium text-gray-700\" htmlFor=\"pageSize\">\n              Show:\n            </label>\n            <select\n              id=\"pageSize\"\n              value={pageSize}\n              onChange={(e) => onPageSizeChange(Number(e.target.value))}\n              className=\"border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {pageSizeOptions.map((size) => (\n                <option key={size} value={size}>{size} per page</option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-1\">\n          <button\n            onClick={() => onPageChange(1)}\n            disabled={currentPage === 1}\n            className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            aria-label=\"First Page\"\n            type=\"button\"\n          >\n            <i className=\"fas fa-angle-double-left\"></i>\n          </button>\n          \n          <button\n            onClick={() => onPageChange(Math.max(currentPage - 1, 1))}\n            disabled={currentPage === 1}\n            className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            aria-label=\"Previous Page\"\n            type=\"button\"\n          >\n            <i className=\"fas fa-angle-left\"></i>\n          </button>\n\n          {renderPageNumbers()}\n\n          <button\n            onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}\n            disabled={currentPage === totalPages}\n            className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            aria-label=\"Next Page\"\n            type=\"button\"\n          >\n            <i className=\"fas fa-angle-right\"></i>\n          </button>\n          \n          <button\n            onClick={() => onPageChange(totalPages)}\n            disabled={currentPage === totalPages}\n            className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            aria-label=\"Last Page\"\n            type=\"button\"\n          >\n            <i className=\"fas fa-angle-double-right\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Pagination;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAY1B,KAAM,CAAAC,UAAqC,CAAGC,IAAA,EAQxC,IARyC,CAC7CC,WAAW,CACXC,UAAU,CACVC,QAAQ,CACRC,UAAU,CACVC,YAAY,CACZC,gBAAgB,CAChBC,eAAe,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CACnC,CAAC,CAAAP,IAAA,CACC,KAAM,CAAAQ,YAAY,CAAG,CAACP,WAAW,CAAG,CAAC,EAAIE,QAAQ,CACjD,KAAM,CAAAM,WAAW,CAAGC,IAAI,CAACC,GAAG,CAACV,WAAW,CAAGE,QAAQ,CAAEC,UAAU,CAAC,CAEhE,KAAM,CAAAQ,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAC,KAAK,CAAG,EAAE,CAChB,KAAM,CAAAC,SAAS,CAAGJ,IAAI,CAACK,GAAG,CAAC,CAAC,CAAEd,WAAW,CAAG,CAAC,CAAC,CAC9C,KAAM,CAAAe,OAAO,CAAGN,IAAI,CAACC,GAAG,CAACT,UAAU,CAAED,WAAW,CAAG,CAAC,CAAC,CAErD,GAAIa,SAAS,CAAG,CAAC,CAAE,CACjBD,KAAK,CAACI,IAAI,cACRrB,IAAA,WAEEsB,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAAC,CAAC,CAAE,CAC/Bc,SAAS,CAAC,+IAA+I,CAAAC,QAAA,CAC1J,GAED,EALO,CAKC,CACV,CAAC,CACD,GAAIN,SAAS,CAAG,CAAC,CAAE,CACjBD,KAAK,CAACI,IAAI,cACRrB,IAAA,SAAsBuB,SAAS,CAAC,wFAAwF,CAAAC,QAAA,CAAC,KAEzH,EAFU,WAEJ,CACR,CAAC,CACH,CACF,CAEA,IAAK,GAAI,CAAAC,CAAC,CAAGP,SAAS,CAAEO,CAAC,EAAIL,OAAO,CAAEK,CAAC,EAAE,CAAE,CACzCR,KAAK,CAACI,IAAI,cACRrB,IAAA,WAEEsB,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAACgB,CAAC,CAAE,CAC/BF,SAAS,CAAE,qFACTE,CAAC,GAAKpB,WAAW,CACb,0CAA0C,CAC1C,6DAA6D,EAChE,CAAAmB,QAAA,CAEFC,CAAC,EARGA,CASC,CACV,CAAC,CACH,CAEA,GAAIL,OAAO,CAAGd,UAAU,CAAE,CACxB,GAAIc,OAAO,CAAGd,UAAU,CAAG,CAAC,CAAE,CAC5BW,KAAK,CAACI,IAAI,cACRrB,IAAA,SAAsBuB,SAAS,CAAC,wFAAwF,CAAAC,QAAA,CAAC,KAEzH,EAFU,WAEJ,CACR,CAAC,CACH,CACAP,KAAK,CAACI,IAAI,cACRrB,IAAA,WAEEsB,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAACH,UAAU,CAAE,CACxCiB,SAAS,CAAC,+IAA+I,CAAAC,QAAA,CAExJlB,UAAU,EAJNA,UAKC,CACV,CAAC,CACH,CAEA,MAAO,CAAAW,KAAK,CACd,CAAC,CAED,GAAIT,UAAU,GAAK,CAAC,CAAE,MAAO,KAAI,CAEjC,mBACER,IAAA,QAAKuB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DtB,KAAA,QAAKqB,SAAS,CAAC,qFAAqF,CAAAC,QAAA,eAClGtB,KAAA,QAAKqB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CtB,KAAA,QAAKqB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,UAC7B,cAAAxB,IAAA,SAAMuB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEZ,YAAY,CAAG,CAAC,CAAO,CAAC,MAAG,CAAC,GAAG,cACtEZ,IAAA,SAAMuB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEX,WAAW,CAAO,CAAC,MAAG,CAAC,GAAG,cACzDb,IAAA,SAAMuB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEhB,UAAU,CAAO,CAAC,WACnD,EAAK,CAAC,cACNN,KAAA,QAAKqB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CxB,IAAA,UAAOuB,SAAS,CAAC,mCAAmC,CAACG,OAAO,CAAC,UAAU,CAAAF,QAAA,CAAC,OAExE,CAAO,CAAC,cACRxB,IAAA,WACE2B,EAAE,CAAC,UAAU,CACbC,KAAK,CAAErB,QAAS,CAChBsB,QAAQ,CAAGC,CAAC,EAAKpB,gBAAgB,CAACqB,MAAM,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,CAAE,CAC1DL,SAAS,CAAC,kIAAkI,CAAAC,QAAA,CAE3Ib,eAAe,CAACsB,GAAG,CAAEC,IAAI,eACxBhC,KAAA,WAAmB0B,KAAK,CAAEM,IAAK,CAAAV,QAAA,EAAEU,IAAI,CAAC,WAAS,GAAlCA,IAA0C,CACxD,CAAC,CACI,CAAC,EACN,CAAC,EACH,CAAC,cAENhC,KAAA,QAAKqB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CxB,IAAA,WACEsB,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAAC,CAAC,CAAE,CAC/B0B,QAAQ,CAAE9B,WAAW,GAAK,CAAE,CAC5BkB,SAAS,CAAC,iMAAiM,CAC3M,aAAW,YAAY,CACvBa,IAAI,CAAC,QAAQ,CAAAZ,QAAA,cAEbxB,IAAA,MAAGuB,SAAS,CAAC,0BAA0B,CAAI,CAAC,CACtC,CAAC,cAETvB,IAAA,WACEsB,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAACK,IAAI,CAACK,GAAG,CAACd,WAAW,CAAG,CAAC,CAAE,CAAC,CAAC,CAAE,CAC1D8B,QAAQ,CAAE9B,WAAW,GAAK,CAAE,CAC5BkB,SAAS,CAAC,+LAA+L,CACzM,aAAW,eAAe,CAC1Ba,IAAI,CAAC,QAAQ,CAAAZ,QAAA,cAEbxB,IAAA,MAAGuB,SAAS,CAAC,mBAAmB,CAAI,CAAC,CAC/B,CAAC,CAERP,iBAAiB,CAAC,CAAC,cAEpBhB,IAAA,WACEsB,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAACK,IAAI,CAACC,GAAG,CAACV,WAAW,CAAG,CAAC,CAAEC,UAAU,CAAC,CAAE,CACnE6B,QAAQ,CAAE9B,WAAW,GAAKC,UAAW,CACrCiB,SAAS,CAAC,+LAA+L,CACzM,aAAW,WAAW,CACtBa,IAAI,CAAC,QAAQ,CAAAZ,QAAA,cAEbxB,IAAA,MAAGuB,SAAS,CAAC,oBAAoB,CAAI,CAAC,CAChC,CAAC,cAETvB,IAAA,WACEsB,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAACH,UAAU,CAAE,CACxC6B,QAAQ,CAAE9B,WAAW,GAAKC,UAAW,CACrCiB,SAAS,CAAC,iMAAiM,CAC3M,aAAW,WAAW,CACtBa,IAAI,CAAC,QAAQ,CAAAZ,QAAA,cAEbxB,IAAA,MAAGuB,SAAS,CAAC,2BAA2B,CAAI,CAAC,CACvC,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}