{"ast": null, "code": "import axios from \"axios\";\nconst BASE_URL = process.env.REACT_APP_BASE_API || 'https://dc66246f10d9.ngrok-free.apps';\n\n// -------------------- Types --------------------\n\n// -------------------- Auth Utilities --------------------\nexport const AUTH_STORAGE_KEY = 'admin_dashboard_auth';\nexport const authUtils = {\n  isAuthenticated: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return false;\n    try {\n      const parsed = JSON.parse(authData);\n      return parsed.isAuthenticated === true;\n    } catch {\n      return false;\n    }\n  },\n  login: credentials => {\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      const authData = {\n        isAuthenticated: true,\n        username: credentials.username,\n        loginTime: new Date().toISOString()\n      };\n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));\n      return {\n        success: true,\n        message: 'Login successful',\n        token: 'mock-jwt-token'\n      };\n    }\n    return {\n      success: false,\n      message: 'Invalid credentials. Please try again.'\n    };\n  },\n  logout: () => {\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n  },\n  getCurrentUser: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return null;\n    try {\n      return JSON.parse(authData);\n    } catch {\n      return null;\n    }\n  }\n};\n\n// -------------------- API Service --------------------\nexport const apiService = {\n  async getDetails() {\n    try {\n      const response = await axios.get(`${BASE_URL}/log/list`, {\n        headers: {\n          'Content-Type': 'application/json',\n          'Accept': 'application/json'\n        }\n      });\n      console.log(response, \">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\");\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      throw error;\n    }\n  },\n  async getUserById(id) {\n    try {\n      const response = await axios.get(`${BASE_URL}/users/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error(`Error fetching user ${id}:`, error);\n      throw error;\n    }\n  },\n  async getDashboardStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 500));\n      return {\n        google: {\n          count: 55,\n          change: 8,\n          trend: 'up'\n        },\n        tradeindia: {\n          count: 34,\n          change: 5,\n          trend: 'down'\n        },\n        indiamart: {\n          count: 42,\n          change: 12,\n          trend: 'up'\n        }\n      };\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      throw error;\n    }\n  },\n  async getRecentActivity() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 300));\n      return [{\n        id: 1,\n        message: 'New user registered',\n        time: '2 mins ago',\n        type: 'success'\n      }, {\n        id: 2,\n        message: 'Transaction completed',\n        time: '5 mins ago',\n        type: 'info'\n      }, {\n        id: 3,\n        message: 'System update available',\n        time: '1 hour ago',\n        type: 'warning'\n      }];\n    } catch (error) {\n      console.error('Error fetching recent activity:', error);\n      throw error;\n    }\n  },\n  async getQuickStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 200));\n      return {\n        totalUsers: 1234,\n        activeSessions: 89,\n        revenue: '$12,345'\n      };\n    } catch (error) {\n      console.error('Error fetching quick stats:', error);\n      throw error;\n    }\n  }\n};\n\n// For backward compatibility\nexport const api = apiService;\nexport default apiService;", "map": {"version": 3, "names": ["axios", "BASE_URL", "process", "env", "REACT_APP_BASE_API", "AUTH_STORAGE_KEY", "authUtils", "isAuthenticated", "authData", "localStorage", "getItem", "parsed", "JSON", "parse", "login", "credentials", "username", "password", "loginTime", "Date", "toISOString", "setItem", "stringify", "success", "message", "token", "logout", "removeItem", "getCurrentUser", "apiService", "getDetails", "response", "get", "headers", "console", "log", "data", "error", "getUserById", "id", "getDashboardStats", "Promise", "resolve", "setTimeout", "google", "count", "change", "trend", "tradeindia", "indiamart", "getRecentActivity", "time", "type", "getQuickStats", "totalUsers", "activeSessions", "revenue", "api"], "sources": ["D:/ELGI/src/api.ts"], "sourcesContent": ["import axios from \"axios\";\n\nconst BASE_URL = process.env.REACT_APP_BASE_API || 'https://dc66246f10d9.ngrok-free.apps';\n\n// -------------------- Types --------------------\nexport interface User {\n  id: number;\n  name: string;\n  username: string;\n  email: string;\n  address: {\n    street: string;\n    suite: string;\n    city: string;\n    zipcode: string;\n    geo: {\n      lat: string;\n      lng: string;\n    };\n  };\n  phone: string;\n  website: string;\n  company: {\n    name: string;\n    catchPhrase: string;\n    bs: string;\n  };\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message?: string;\n  token?: string;\n}\n\n// -------------------- Auth Utilities --------------------\nexport const AUTH_STORAGE_KEY = 'admin_dashboard_auth';\n\nexport const authUtils = {\n  isAuthenticated: (): boolean => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return false;\n\n    try {\n      const parsed = JSON.parse(authData);\n      return parsed.isAuthenticated === true;\n    } catch {\n      return false;\n    }\n  },\n\n  login: (credentials: LoginCredentials): AuthResponse => {\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      const authData = {\n        isAuthenticated: true,\n        username: credentials.username,\n        loginTime: new Date().toISOString(),\n      };\n\n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));\n\n      return {\n        success: true,\n        message: 'Login successful',\n        token: 'mock-jwt-token'\n      };\n    }\n\n    return {\n      success: false,\n      message: 'Invalid credentials. Please try again.'\n    };\n  },\n\n  logout: (): void => {\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n  },\n\n  getCurrentUser: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return null;\n\n    try {\n      return JSON.parse(authData);\n    } catch {\n      return null;\n    }\n  }\n};\n\n// -------------------- API Service --------------------\nexport const apiService = {\n  async getDetails() {\n    try {\n      const response = await axios.get(`${BASE_URL}/log/list`, {\n        headers: {\n          'Content-Type': 'application/json',\n          'Accept': 'application/json'\n        }\n      });\n      console.log(response,\">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\")\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      throw error;\n    }\n  },\n\n  async getUserById(id: number): Promise<User> {\n    try {\n      const response = await axios.get(`${BASE_URL}/users/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error(`Error fetching user ${id}:`, error);\n      throw error;\n    }\n  },\n\n  async getDashboardStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 500));\n      return {\n        google: { count: 55, change: 8, trend: 'up' as const },\n        tradeindia: { count: 34, change: 5, trend: 'down' as const },\n        indiamart: { count: 42, change: 12, trend: 'up' as const }\n      };\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      throw error;\n    }\n  },\n\n  async getRecentActivity() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 300));\n      return [\n        { id: 1, message: 'New user registered', time: '2 mins ago', type: 'success' as const },\n        { id: 2, message: 'Transaction completed', time: '5 mins ago', type: 'info' as const },\n        { id: 3, message: 'System update available', time: '1 hour ago', type: 'warning' as const }\n      ];\n    } catch (error) {\n      console.error('Error fetching recent activity:', error);\n      throw error;\n    }\n  },\n\n  async getQuickStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 200));\n      return {\n        totalUsers: 1234,\n        activeSessions: 89,\n        revenue: '$12,345'\n      };\n    } catch (error) {\n      console.error('Error fetching quick stats:', error);\n      throw error;\n    }\n  }\n};\n\n// For backward compatibility\nexport const api = apiService;\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,IAAI,sCAAsC;;AAEzF;;AAoCA;AACA,OAAO,MAAMC,gBAAgB,GAAG,sBAAsB;AAEtD,OAAO,MAAMC,SAAS,GAAG;EACvBC,eAAe,EAAEA,CAAA,KAAe;IAC9B,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC;IACvD,IAAI,CAACG,QAAQ,EAAE,OAAO,KAAK;IAE3B,IAAI;MACF,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;MACnC,OAAOG,MAAM,CAACJ,eAAe,KAAK,IAAI;IACxC,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF,CAAC;EAEDO,KAAK,EAAGC,WAA6B,IAAmB;IACtD,IAAIA,WAAW,CAACC,QAAQ,KAAK,OAAO,IAAID,WAAW,CAACE,QAAQ,KAAK,WAAW,EAAE;MAC5E,MAAMT,QAAQ,GAAG;QACfD,eAAe,EAAE,IAAI;QACrBS,QAAQ,EAAED,WAAW,CAACC,QAAQ;QAC9BE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAEDX,YAAY,CAACY,OAAO,CAAChB,gBAAgB,EAAEO,IAAI,CAACU,SAAS,CAACd,QAAQ,CAAC,CAAC;MAEhE,OAAO;QACLe,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,kBAAkB;QAC3BC,KAAK,EAAE;MACT,CAAC;IACH;IAEA,OAAO;MACLF,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAEDE,MAAM,EAAEA,CAAA,KAAY;IAClBjB,YAAY,CAACkB,UAAU,CAACtB,gBAAgB,CAAC;EAC3C,CAAC;EAEDuB,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAMpB,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC;IACvD,IAAI,CAACG,QAAQ,EAAE,OAAO,IAAI;IAE1B,IAAI;MACF,OAAOI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;IAC7B,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMqB,UAAU,GAAG;EACxB,MAAMC,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,GAAG,CAAC,GAAG/B,QAAQ,WAAW,EAAE;QACvDgC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MACFC,OAAO,CAACC,GAAG,CAACJ,QAAQ,EAAC,gCAAgC,CAAC;MACtD,OAAOA,QAAQ,CAACK,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMC,WAAWA,CAACC,EAAU,EAAiB;IAC3C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,GAAG,CAAC,GAAG/B,QAAQ,UAAUsC,EAAE,EAAE,CAAC;MAC3D,OAAOR,QAAQ,CAACK,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuBE,EAAE,GAAG,EAAEF,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMG,iBAAiBA,CAAA,EAAG;IACxB,IAAI;MACF,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MACtD,OAAO;QACLE,MAAM,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAc,CAAC;QACtDC,UAAU,EAAE;UAAEH,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAgB,CAAC;QAC5DE,SAAS,EAAE;UAAEJ,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAc;MAC3D,CAAC;IACH,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMa,iBAAiBA,CAAA,EAAG;IACxB,IAAI;MACF,MAAM,IAAIT,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MACtD,OAAO,CACL;QAAEH,EAAE,EAAE,CAAC;QAAEf,OAAO,EAAE,qBAAqB;QAAE2B,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAmB,CAAC,EACvF;QAAEb,EAAE,EAAE,CAAC;QAAEf,OAAO,EAAE,uBAAuB;QAAE2B,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAgB,CAAC,EACtF;QAAEb,EAAE,EAAE,CAAC;QAAEf,OAAO,EAAE,yBAAyB;QAAE2B,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAmB,CAAC,CAC5F;IACH,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMgB,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAM,IAAIZ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MACtD,OAAO;QACLY,UAAU,EAAE,IAAI;QAChBC,cAAc,EAAE,EAAE;QAClBC,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMoB,GAAG,GAAG5B,UAAU;AAC7B,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}