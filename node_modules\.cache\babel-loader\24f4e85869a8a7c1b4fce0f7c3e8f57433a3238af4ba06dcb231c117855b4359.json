{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ErrorMessage=_ref=>{let{message,onRetry,className=''}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:`text-center py-16 ${className}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center space-y-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-exclamation-triangle text-red-600 text-2xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-red-600 font-medium mb-2\",children:message}),onRetry&&/*#__PURE__*/_jsxs(\"button\",{onClick:onRetry,className:\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\",type:\"button\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-redo mr-2\"}),\"Try Again\"]})]})]})});};export default ErrorMessage;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "ErrorMessage", "_ref", "message", "onRetry", "className", "children", "onClick", "type"], "sources": ["D:/ELGI/src/components/common/ErrorMessage.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onRetry?: () => void;\n  className?: string;\n}\n\nconst ErrorMessage: React.FC<ErrorMessageProps> = ({ \n  message, \n  onRetry, \n  className = '' \n}) => {\n  return (\n    <div className={`text-center py-16 ${className}`}>\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center\">\n          <i className=\"fas fa-exclamation-triangle text-red-600 text-2xl\" />\n        </div>\n        <div>\n          <p className=\"text-red-600 font-medium mb-2\">{message}</p>\n          {onRetry && (\n            <button\n              onClick={onRetry}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\"\n              type=\"button\"\n            >\n              <i className=\"fas fa-redo mr-2\"></i>\n              Try Again\n            </button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ErrorMessage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ1B,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAI5C,IAJ6C,CACjDC,OAAO,CACPC,OAAO,CACPC,SAAS,CAAG,EACd,CAAC,CAAAH,IAAA,CACC,mBACEJ,IAAA,QAAKO,SAAS,CAAE,qBAAqBA,SAAS,EAAG,CAAAC,QAAA,cAC/CN,KAAA,QAAKK,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnDR,IAAA,QAAKO,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cACjFR,IAAA,MAAGO,SAAS,CAAC,mDAAmD,CAAE,CAAC,CAChE,CAAC,cACNL,KAAA,QAAAM,QAAA,eACER,IAAA,MAAGO,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAEH,OAAO,CAAI,CAAC,CACzDC,OAAO,eACNJ,KAAA,WACEO,OAAO,CAAEH,OAAQ,CACjBC,SAAS,CAAC,2FAA2F,CACrGG,IAAI,CAAC,QAAQ,CAAAF,QAAA,eAEbR,IAAA,MAAGO,SAAS,CAAC,kBAAkB,CAAI,CAAC,YAEtC,EAAQ,CACT,EACE,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}