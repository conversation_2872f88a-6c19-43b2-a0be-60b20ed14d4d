{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\Login\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const {\n    login,\n    isAuthenticated\n  } = useAuth();\n  const location = useLocation();\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Redirect if already authenticated\n  if (isAuthenticated) {\n    var _location$state, _location$state$from;\n    const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/dashboard';\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: from,\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 12\n    }, this);\n  }\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    // Client-side validation for exact credentials\n    if (formData.username !== 'admin') {\n      setError('Invalid username. Please enter \"admin\".');\n      setIsLoading(false);\n      return;\n    }\n    if (formData.password !== 'admin@123') {\n      setError('Invalid password. Please enter \"admin@123\".');\n      setIsLoading(false);\n      return;\n    }\n    try {\n      const result = login(formData);\n      if (!result.success) {\n        setError(result.message || 'Login failed');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col justify-center px-12 lg:px-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md w-full mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-12 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 rounded-2xl flex items-center justify-center mb-6 mx-auto shadow-lg\",\n            style: {\n              backgroundColor: '#3579F3'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-cube text-white text-2xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mt-2\",\n            children: \"Admin Portal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: \"Welcome Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Please sign in to your account to continue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"username\",\n                className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-user text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"username\",\n                  name: \"username\",\n                  type: \"text\",\n                  placeholder: \"admin\",\n                  value: formData.username,\n                  onChange: handleInputChange,\n                  className: \"w-full pl-10 pr-4 py-4 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-lock text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"password\",\n                  name: \"password\",\n                  type: \"password\",\n                  placeholder: \"Enter your password\",\n                  value: formData.password,\n                  onChange: handleInputChange,\n                  className: \"w-full pl-10 pr-4 py-4 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 text-red-600 text-sm bg-red-50 border border-red-200 rounded-xl p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-exclamation-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"w-full bg-blue-600 hover:bg-blue-700 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg hover:shadow-xl\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this), \"Signing In...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-sign-in-alt mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this), \"Sign In\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:flex lg:flex-1 bg-gradient-to-br from-blue-600 to-indigo-700 flex-col justify-center items-center px-12 text-white relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black opacity-10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 max-w-md text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-chart-line text-3xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold mb-4\",\n            children: \"Welcome Back!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-100 text-lg leading-relaxed\",\n            children: \"Access your admin dashboard to manage transactions, monitor performance, and stay in control of your business operations.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-3 gap-4 mt-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-shield-alt text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-100\",\n              children: \"Secure\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-tachometer-alt text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-100\",\n              children: \"Fast\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-users text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-100\",\n              children: \"Reliable\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"HgHLd4hink56jqDRfUD7BS9O3lo=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "login", "isAuthenticated", "location", "formData", "setFormData", "username", "password", "error", "setError", "isLoading", "setIsLoading", "_location$state", "_location$state$from", "from", "state", "pathname", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "result", "success", "message", "err", "className", "children", "style", "backgroundColor", "onSubmit", "htmlFor", "id", "type", "placeholder", "onChange", "required", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/Login/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { LoginCredentials } from '../../api';\n\nconst Login: React.FC = () => {\n  const { login, isAuthenticated } = useAuth();\n  const location = useLocation();\n  \n  const [formData, setFormData] = useState<LoginCredentials>({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState<string>('');\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n\n  // Redirect if already authenticated\n  if (isAuthenticated) {\n    const from = location.state?.from?.pathname || '/dashboard';\n    return <Navigate to={from} replace />;\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    // Client-side validation for exact credentials\n    if (formData.username !== 'admin') {\n      setError('Invalid username. Please enter \"admin\".');\n      setIsLoading(false);\n      return;\n    }\n\n    if (formData.password !== 'admin@123') {\n      setError('Invalid password. Please enter \"admin@123\".');\n      setIsLoading(false);\n      return;\n    }\n\n    try {\n      const result = login(formData);\n\n      if (!result.success) {\n        setError(result.message || 'Login failed');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex\">\n      <div className=\"flex-1 flex flex-col justify-center px-12 lg:px-20\">\n        <div className=\"max-w-md w-full mx-auto\">\n          <div className=\"mb-12 text-center\">\n            <div className=\"w-16 h-16 rounded-2xl flex items-center justify-center mb-6 mx-auto shadow-lg\" style={{ backgroundColor: '#3579F3' }}>\n              <i className=\"fas fa-cube text-white text-2xl\"></i>\n            </div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n            <p className=\"text-gray-600 mt-2\">Admin Portal</p>\n          </div>\n\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">Welcome Back</h2>\n              <p className=\"text-gray-600\">\n                Please sign in to your account to continue\n              </p>\n            </div>\n\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"username\" className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                  Username\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <i className=\"fas fa-user text-gray-400\"></i>\n                  </div>\n                  <input\n                    id=\"username\"\n                    name=\"username\"\n                    type=\"text\"\n                    placeholder=\"admin\"\n                    value={formData.username}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-4 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    required\n                    disabled={isLoading}\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <i className=\"fas fa-lock text-gray-400\"></i>\n                  </div>\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type=\"password\"\n                    placeholder=\"Enter your password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-4 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    required\n                    disabled={isLoading}\n                  />\n                </div>\n              </div>\n\n              {error && (\n                <div className=\"flex items-center space-x-2 text-red-600 text-sm bg-red-50 border border-red-200 rounded-xl p-4\">\n                  <i className=\"fas fa-exclamation-circle\"></i>\n                  <span>{error}</span>\n                </div>\n              )}\n\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full bg-blue-600 hover:bg-blue-700 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg hover:shadow-xl\"\n              >\n                {isLoading ? (\n                  <>\n                    <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3\"></div>\n                    Signing In...\n                  </>\n                ) : (\n                  <>\n                    <i className=\"fas fa-sign-in-alt mr-2\"></i>\n                    Sign In\n                  </>\n                )}\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"hidden lg:flex lg:flex-1 bg-gradient-to-br from-blue-600 to-indigo-700 flex-col justify-center items-center px-12 text-white relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-black opacity-10\"></div>\n        <div className=\"relative z-10 max-w-md text-center\">\n          <div className=\"mb-8\">\n            <div className=\"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <i className=\"fas fa-chart-line text-3xl\"></i>\n            </div>\n            <h2 className=\"text-4xl font-bold mb-4\">Welcome Back!</h2>\n            <p className=\"text-blue-100 text-lg leading-relaxed\">\n              Access your admin dashboard to manage transactions, monitor performance, and stay in control of your business operations.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-3 gap-4 mt-8\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\">\n                <i className=\"fas fa-shield-alt text-xl\"></i>\n              </div>\n              <p className=\"text-sm text-blue-100\">Secure</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\">\n                <i className=\"fas fa-tachometer-alt text-xl\"></i>\n              </div>\n              <p className=\"text-sm text-blue-100\">Fast</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\">\n                <i className=\"fas fa-users text-xl\"></i>\n              </div>\n              <p className=\"text-sm text-blue-100\">Reliable</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,KAAK;IAAEC;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC5C,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAmB;IACzDe,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAU,KAAK,CAAC;;EAE1D;EACA,IAAIW,eAAe,EAAE;IAAA,IAAAU,eAAA,EAAAC,oBAAA;IACnB,MAAMC,IAAI,GAAG,EAAAF,eAAA,GAAAT,QAAQ,CAACY,KAAK,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBE,IAAI,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBG,QAAQ,KAAI,YAAY;IAC3D,oBAAOpB,OAAA,CAACJ,QAAQ;MAACyB,EAAE,EAAEH,IAAK;MAACI,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACvC;EAEA,MAAMC,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCtB,WAAW,CAACuB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH,IAAIlB,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAOL,CAAmC,IAAK;IAClEA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBnB,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAIL,QAAQ,CAACE,QAAQ,KAAK,OAAO,EAAE;MACjCG,QAAQ,CAAC,yCAAyC,CAAC;MACnDE,YAAY,CAAC,KAAK,CAAC;MACnB;IACF;IAEA,IAAIP,QAAQ,CAACG,QAAQ,KAAK,WAAW,EAAE;MACrCE,QAAQ,CAAC,6CAA6C,CAAC;MACvDE,YAAY,CAAC,KAAK,CAAC;MACnB;IACF;IAEA,IAAI;MACF,MAAMoB,MAAM,GAAG9B,KAAK,CAACG,QAAQ,CAAC;MAE9B,IAAI,CAAC2B,MAAM,CAACC,OAAO,EAAE;QACnBvB,QAAQ,CAACsB,MAAM,CAACE,OAAO,IAAI,cAAc,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZzB,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEf,OAAA;IAAKuC,SAAS,EAAC,gEAAgE;IAAAC,QAAA,gBAC7ExC,OAAA;MAAKuC,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eACjExC,OAAA;QAAKuC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCxC,OAAA;UAAKuC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCxC,OAAA;YAAKuC,SAAS,EAAC,+EAA+E;YAACE,KAAK,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAAF,QAAA,eACnIxC,OAAA;cAAGuC,SAAS,EAAC;YAAiC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACN1B,OAAA;YAAIuC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAS;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/D1B,OAAA;YAAGuC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAY;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAEN1B,OAAA;UAAKuC,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACxExC,OAAA;YAAKuC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BxC,OAAA;cAAIuC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAY;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvE1B,OAAA;cAAGuC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN1B,OAAA;YAAM2C,QAAQ,EAAEV,YAAa;YAACM,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDxC,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAO4C,OAAO,EAAC,UAAU;gBAACL,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAErF;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1B,OAAA;gBAAKuC,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBxC,OAAA;kBAAKuC,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnFxC,OAAA;oBAAGuC,SAAS,EAAC;kBAA2B;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACN1B,OAAA;kBACE6C,EAAE,EAAC,UAAU;kBACbhB,IAAI,EAAC,UAAU;kBACfiB,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,OAAO;kBACnBjB,KAAK,EAAEtB,QAAQ,CAACE,QAAS;kBACzBsC,QAAQ,EAAErB,iBAAkB;kBAC5BY,SAAS,EAAC,sLAAsL;kBAChMU,QAAQ;kBACRC,QAAQ,EAAEpC;gBAAU;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1B,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAO4C,OAAO,EAAC,UAAU;gBAACL,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAErF;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1B,OAAA;gBAAKuC,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBxC,OAAA;kBAAKuC,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnFxC,OAAA;oBAAGuC,SAAS,EAAC;kBAA2B;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACN1B,OAAA;kBACE6C,EAAE,EAAC,UAAU;kBACbhB,IAAI,EAAC,UAAU;kBACfiB,IAAI,EAAC,UAAU;kBACfC,WAAW,EAAC,qBAAqB;kBACjCjB,KAAK,EAAEtB,QAAQ,CAACG,QAAS;kBACzBqC,QAAQ,EAAErB,iBAAkB;kBAC5BY,SAAS,EAAC,sLAAsL;kBAChMU,QAAQ;kBACRC,QAAQ,EAAEpC;gBAAU;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELd,KAAK,iBACJZ,OAAA;cAAKuC,SAAS,EAAC,iGAAiG;cAAAC,QAAA,gBAC9GxC,OAAA;gBAAGuC,SAAS,EAAC;cAA2B;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7C1B,OAAA;gBAAAwC,QAAA,EAAO5B;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACN,eAED1B,OAAA;cACE8C,IAAI,EAAC,QAAQ;cACbI,QAAQ,EAAEpC,SAAU;cACpByB,SAAS,EAAC,2NAA2N;cAAAC,QAAA,EAEpO1B,SAAS,gBACRd,OAAA,CAAAE,SAAA;gBAAAsC,QAAA,gBACExC,OAAA;kBAAKuC,SAAS,EAAC;gBAAmF;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,iBAE3G;cAAA,eAAE,CAAC,gBAEH1B,OAAA,CAAAE,SAAA;gBAAAsC,QAAA,gBACExC,OAAA;kBAAGuC,SAAS,EAAC;gBAAyB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,WAE7C;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1B,OAAA;MAAKuC,SAAS,EAAC,uJAAuJ;MAAAC,QAAA,gBACpKxC,OAAA;QAAKuC,SAAS,EAAC;MAAsC;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5D1B,OAAA;QAAKuC,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDxC,OAAA;UAAKuC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxC,OAAA;YAAKuC,SAAS,EAAC,6FAA6F;YAAAC,QAAA,eAC1GxC,OAAA;cAAGuC,SAAS,EAAC;YAA4B;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACN1B,OAAA;YAAIuC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAa;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1D1B,OAAA;YAAGuC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAErD;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN1B,OAAA;UAAKuC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CxC,OAAA;YAAKuC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BxC,OAAA;cAAKuC,SAAS,EAAC,2FAA2F;cAAAC,QAAA,eACxGxC,OAAA;gBAAGuC,SAAS,EAAC;cAA2B;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN1B,OAAA;cAAGuC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAM;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACN1B,OAAA;YAAKuC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BxC,OAAA;cAAKuC,SAAS,EAAC,2FAA2F;cAAAC,QAAA,eACxGxC,OAAA;gBAAGuC,SAAS,EAAC;cAA+B;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN1B,OAAA;cAAGuC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAI;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN1B,OAAA;YAAKuC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BxC,OAAA;cAAKuC,SAAS,EAAC,2FAA2F;cAAAC,QAAA,eACxGxC,OAAA;gBAAGuC,SAAS,EAAC;cAAsB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACN1B,OAAA;cAAGuC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAQ;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CA5LID,KAAe;EAAA,QACgBL,OAAO,EACzBD,WAAW;AAAA;AAAAsD,EAAA,GAFxBhD,KAAe;AA8LrB,eAAeA,KAAK;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}