{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\common\\\\StatusChart.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatusChart = ({\n  data,\n  className = ''\n}) => {\n  const total = data.success + data.pending + data.failed;\n  if (total === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex items-center justify-center h-48 ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"No data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this);\n  }\n  const successPercentage = data.success / total * 100;\n  const pendingPercentage = data.pending / total * 100;\n  const failedPercentage = data.failed / total * 100;\n\n  // Donut chart implementation\n  const radius = 70;\n  const strokeWidth = 20;\n  const normalizedRadius = radius - strokeWidth * 0.5;\n  const circumference = normalizedRadius * 2 * Math.PI;\n  const successStrokeDasharray = `${successPercentage / 100 * circumference} ${circumference}`;\n  const pendingStrokeDasharray = `${pendingPercentage / 100 * circumference} ${circumference}`;\n  const failedStrokeDasharray = `${failedPercentage / 100 * circumference} ${circumference}`;\n  const successOffset = 0;\n  const pendingOffset = -(successPercentage / 100) * circumference;\n  const failedOffset = -((successPercentage + pendingPercentage) / 100) * circumference;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex flex-col items-center ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        height: radius * 2,\n        width: radius * 2,\n        className: \"transform -rotate-90\",\n        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n          stroke: \"#f3f4f6\",\n          fill: \"transparent\",\n          strokeWidth: strokeWidth,\n          r: normalizedRadius,\n          cx: radius,\n          cy: radius\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), data.success > 0 && /*#__PURE__*/_jsxDEV(\"circle\", {\n          stroke: \"#10b981\",\n          fill: \"transparent\",\n          strokeWidth: strokeWidth,\n          strokeDasharray: successStrokeDasharray,\n          strokeDashoffset: successOffset,\n          strokeLinecap: \"round\",\n          r: normalizedRadius,\n          cx: radius,\n          cy: radius,\n          className: \"transition-all duration-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this), data.pending > 0 && /*#__PURE__*/_jsxDEV(\"circle\", {\n          stroke: \"#f59e0b\",\n          fill: \"transparent\",\n          strokeWidth: strokeWidth,\n          strokeDasharray: pendingStrokeDasharray,\n          strokeDashoffset: pendingOffset,\n          strokeLinecap: \"round\",\n          r: normalizedRadius,\n          cx: radius,\n          cy: radius,\n          className: \"transition-all duration-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), data.failed > 0 && /*#__PURE__*/_jsxDEV(\"circle\", {\n          stroke: \"#ef4444\",\n          fill: \"transparent\",\n          strokeWidth: strokeWidth,\n          strokeDasharray: failedStrokeDasharray,\n          strokeDashoffset: failedOffset,\n          strokeLinecap: \"round\",\n          r: normalizedRadius,\n          cx: radius,\n          cy: radius,\n          className: \"transition-all duration-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 flex flex-col items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: total\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Total\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-3 h-3 bg-green-500 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-700\",\n          children: [\"Success (\", successPercentage.toFixed(1), \"%)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-3 h-3 bg-yellow-500 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-700\",\n          children: [\"Pending (\", pendingPercentage.toFixed(1), \"%)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-3 h-3 bg-red-500 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-700\",\n          children: [\"Failed (\", failedPercentage.toFixed(1), \"%)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_c = StatusChart;\nexport default StatusChart;\nvar _c;\n$RefreshReg$(_c, \"StatusChart\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "StatusChart", "data", "className", "total", "success", "pending", "failed", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "successPercentage", "pendingPercentage", "failedPercentage", "radius", "strokeWidth", "normalizedRadius", "circumference", "Math", "PI", "successStrokeDasharray", "pendingStrokeDasharray", "failedStrokeDasharray", "successOffset", "pendingOffset", "failedOffset", "height", "width", "stroke", "fill", "r", "cx", "cy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeLinecap", "toFixed", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/common/StatusChart.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface StatusData {\n  success: number;\n  pending: number;\n  failed: number;\n}\n\ninterface StatusChartProps {\n  data: StatusData;\n  className?: string;\n}\n\nconst StatusChart: React.FC<StatusChartProps> = ({ data, className = '' }) => {\n  const total = data.success + data.pending + data.failed;\n  \n  if (total === 0) {\n    return (\n      <div className={`flex items-center justify-center h-48 ${className}`}>\n        <p className=\"text-gray-500\">No data available</p>\n      </div>\n    );\n  }\n\n  const successPercentage = (data.success / total) * 100;\n  const pendingPercentage = (data.pending / total) * 100;\n  const failedPercentage = (data.failed / total) * 100;\n\n  // Donut chart implementation\n  const radius = 70;\n  const strokeWidth = 20;\n  const normalizedRadius = radius - strokeWidth * 0.5;\n  const circumference = normalizedRadius * 2 * Math.PI;\n\n  const successStrokeDasharray = `${(successPercentage / 100) * circumference} ${circumference}`;\n  const pendingStrokeDasharray = `${(pendingPercentage / 100) * circumference} ${circumference}`;\n  const failedStrokeDasharray = `${(failedPercentage / 100) * circumference} ${circumference}`;\n\n  const successOffset = 0;\n  const pendingOffset = -(successPercentage / 100) * circumference;\n  const failedOffset = -((successPercentage + pendingPercentage) / 100) * circumference;\n\n  return (\n    <div className={`flex flex-col items-center ${className}`}>\n      <div className=\"relative\">\n        <svg\n          height={radius * 2}\n          width={radius * 2}\n          className=\"transform -rotate-90\"\n        >\n          {/* Background circle */}\n          <circle\n            stroke=\"#f3f4f6\"\n            fill=\"transparent\"\n            strokeWidth={strokeWidth}\n            r={normalizedRadius}\n            cx={radius}\n            cy={radius}\n          />\n          \n          {/* Success arc */}\n          {data.success > 0 && (\n            <circle\n              stroke=\"#10b981\"\n              fill=\"transparent\"\n              strokeWidth={strokeWidth}\n              strokeDasharray={successStrokeDasharray}\n              strokeDashoffset={successOffset}\n              strokeLinecap=\"round\"\n              r={normalizedRadius}\n              cx={radius}\n              cy={radius}\n              className=\"transition-all duration-500\"\n            />\n          )}\n          \n          {/* Pending arc */}\n          {data.pending > 0 && (\n            <circle\n              stroke=\"#f59e0b\"\n              fill=\"transparent\"\n              strokeWidth={strokeWidth}\n              strokeDasharray={pendingStrokeDasharray}\n              strokeDashoffset={pendingOffset}\n              strokeLinecap=\"round\"\n              r={normalizedRadius}\n              cx={radius}\n              cy={radius}\n              className=\"transition-all duration-500\"\n            />\n          )}\n          \n          {/* Failed arc */}\n          {data.failed > 0 && (\n            <circle\n              stroke=\"#ef4444\"\n              fill=\"transparent\"\n              strokeWidth={strokeWidth}\n              strokeDasharray={failedStrokeDasharray}\n              strokeDashoffset={failedOffset}\n              strokeLinecap=\"round\"\n              r={normalizedRadius}\n              cx={radius}\n              cy={radius}\n              className=\"transition-all duration-500\"\n            />\n          )}\n        </svg>\n        \n        {/* Center text */}\n        <div className=\"absolute inset-0 flex flex-col items-center justify-center\">\n          <span className=\"text-2xl font-bold text-gray-900\">{total}</span>\n          <span className=\"text-sm text-gray-500\">Total</span>\n        </div>\n      </div>\n      \n      {/* Legend */}\n      <div className=\"mt-4 space-y-2\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n          <span className=\"text-sm text-gray-700\">Success ({successPercentage.toFixed(1)}%)</span>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n          <span className=\"text-sm text-gray-700\">Pending ({pendingPercentage.toFixed(1)}%)</span>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n          <span className=\"text-sm text-gray-700\">Failed ({failedPercentage.toFixed(1)}%)</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StatusChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa1B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAC5E,MAAMC,KAAK,GAAGF,IAAI,CAACG,OAAO,GAAGH,IAAI,CAACI,OAAO,GAAGJ,IAAI,CAACK,MAAM;EAEvD,IAAIH,KAAK,KAAK,CAAC,EAAE;IACf,oBACEJ,OAAA;MAAKG,SAAS,EAAE,yCAAyCA,SAAS,EAAG;MAAAK,QAAA,eACnER,OAAA;QAAGG,SAAS,EAAC,eAAe;QAAAK,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEV;EAEA,MAAMC,iBAAiB,GAAIX,IAAI,CAACG,OAAO,GAAGD,KAAK,GAAI,GAAG;EACtD,MAAMU,iBAAiB,GAAIZ,IAAI,CAACI,OAAO,GAAGF,KAAK,GAAI,GAAG;EACtD,MAAMW,gBAAgB,GAAIb,IAAI,CAACK,MAAM,GAAGH,KAAK,GAAI,GAAG;;EAEpD;EACA,MAAMY,MAAM,GAAG,EAAE;EACjB,MAAMC,WAAW,GAAG,EAAE;EACtB,MAAMC,gBAAgB,GAAGF,MAAM,GAAGC,WAAW,GAAG,GAAG;EACnD,MAAME,aAAa,GAAGD,gBAAgB,GAAG,CAAC,GAAGE,IAAI,CAACC,EAAE;EAEpD,MAAMC,sBAAsB,GAAG,GAAIT,iBAAiB,GAAG,GAAG,GAAIM,aAAa,IAAIA,aAAa,EAAE;EAC9F,MAAMI,sBAAsB,GAAG,GAAIT,iBAAiB,GAAG,GAAG,GAAIK,aAAa,IAAIA,aAAa,EAAE;EAC9F,MAAMK,qBAAqB,GAAG,GAAIT,gBAAgB,GAAG,GAAG,GAAII,aAAa,IAAIA,aAAa,EAAE;EAE5F,MAAMM,aAAa,GAAG,CAAC;EACvB,MAAMC,aAAa,GAAG,EAAEb,iBAAiB,GAAG,GAAG,CAAC,GAAGM,aAAa;EAChE,MAAMQ,YAAY,GAAG,EAAE,CAACd,iBAAiB,GAAGC,iBAAiB,IAAI,GAAG,CAAC,GAAGK,aAAa;EAErF,oBACEnB,OAAA;IAAKG,SAAS,EAAE,8BAA8BA,SAAS,EAAG;IAAAK,QAAA,gBACxDR,OAAA;MAAKG,SAAS,EAAC,UAAU;MAAAK,QAAA,gBACvBR,OAAA;QACE4B,MAAM,EAAEZ,MAAM,GAAG,CAAE;QACnBa,KAAK,EAAEb,MAAM,GAAG,CAAE;QAClBb,SAAS,EAAC,sBAAsB;QAAAK,QAAA,gBAGhCR,OAAA;UACE8B,MAAM,EAAC,SAAS;UAChBC,IAAI,EAAC,aAAa;UAClBd,WAAW,EAAEA,WAAY;UACzBe,CAAC,EAAEd,gBAAiB;UACpBe,EAAE,EAAEjB,MAAO;UACXkB,EAAE,EAAElB;QAAO;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,EAGDV,IAAI,CAACG,OAAO,GAAG,CAAC,iBACfL,OAAA;UACE8B,MAAM,EAAC,SAAS;UAChBC,IAAI,EAAC,aAAa;UAClBd,WAAW,EAAEA,WAAY;UACzBkB,eAAe,EAAEb,sBAAuB;UACxCc,gBAAgB,EAAEX,aAAc;UAChCY,aAAa,EAAC,OAAO;UACrBL,CAAC,EAAEd,gBAAiB;UACpBe,EAAE,EAAEjB,MAAO;UACXkB,EAAE,EAAElB,MAAO;UACXb,SAAS,EAAC;QAA6B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACF,EAGAV,IAAI,CAACI,OAAO,GAAG,CAAC,iBACfN,OAAA;UACE8B,MAAM,EAAC,SAAS;UAChBC,IAAI,EAAC,aAAa;UAClBd,WAAW,EAAEA,WAAY;UACzBkB,eAAe,EAAEZ,sBAAuB;UACxCa,gBAAgB,EAAEV,aAAc;UAChCW,aAAa,EAAC,OAAO;UACrBL,CAAC,EAAEd,gBAAiB;UACpBe,EAAE,EAAEjB,MAAO;UACXkB,EAAE,EAAElB,MAAO;UACXb,SAAS,EAAC;QAA6B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACF,EAGAV,IAAI,CAACK,MAAM,GAAG,CAAC,iBACdP,OAAA;UACE8B,MAAM,EAAC,SAAS;UAChBC,IAAI,EAAC,aAAa;UAClBd,WAAW,EAAEA,WAAY;UACzBkB,eAAe,EAAEX,qBAAsB;UACvCY,gBAAgB,EAAET,YAAa;UAC/BU,aAAa,EAAC,OAAO;UACrBL,CAAC,EAAEd,gBAAiB;UACpBe,EAAE,EAAEjB,MAAO;UACXkB,EAAE,EAAElB,MAAO;UACXb,SAAS,EAAC;QAA6B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNZ,OAAA;QAAKG,SAAS,EAAC,4DAA4D;QAAAK,QAAA,gBACzER,OAAA;UAAMG,SAAS,EAAC,kCAAkC;UAAAK,QAAA,EAAEJ;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjEZ,OAAA;UAAMG,SAAS,EAAC,uBAAuB;UAAAK,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNZ,OAAA;MAAKG,SAAS,EAAC,gBAAgB;MAAAK,QAAA,gBAC7BR,OAAA;QAAKG,SAAS,EAAC,6BAA6B;QAAAK,QAAA,gBAC1CR,OAAA;UAAKG,SAAS,EAAC;QAAmC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDZ,OAAA;UAAMG,SAAS,EAAC,uBAAuB;UAAAK,QAAA,GAAC,WAAS,EAACK,iBAAiB,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;QAAA;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC,eACNZ,OAAA;QAAKG,SAAS,EAAC,6BAA6B;QAAAK,QAAA,gBAC1CR,OAAA;UAAKG,SAAS,EAAC;QAAoC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DZ,OAAA;UAAMG,SAAS,EAAC,uBAAuB;UAAAK,QAAA,GAAC,WAAS,EAACM,iBAAiB,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;QAAA;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC,eACNZ,OAAA;QAAKG,SAAS,EAAC,6BAA6B;QAAAK,QAAA,gBAC1CR,OAAA;UAAKG,SAAS,EAAC;QAAiC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvDZ,OAAA;UAAMG,SAAS,EAAC,uBAAuB;UAAAK,QAAA,GAAC,UAAQ,EAACO,gBAAgB,CAACuB,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;QAAA;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC2B,EAAA,GAxHItC,WAAuC;AA0H7C,eAAeA,WAAW;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}