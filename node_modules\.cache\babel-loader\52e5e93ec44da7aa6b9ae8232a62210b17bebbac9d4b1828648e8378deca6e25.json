{"ast": null, "code": "import React from'react';import{Navigate,useLocation}from'react-router-dom';import{useAuth}from'../contexts/AuthContext';import{LoadingSpinner}from'./common';import{jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children}=_ref;const{isAuthenticated,loading}=useAuth();const location=useLocation();if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen flex items-center justify-center\",children:/*#__PURE__*/_jsx(LoadingSpinner,{size:\"lg\",message:\"Loading...\"})});}if(!isAuthenticated){// Redirect to login page with return url\nreturn/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}return/*#__PURE__*/_jsx(_Fragment,{children:children});};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "LoadingSpinner", "jsx", "_jsx", "Fragment", "_Fragment", "ProtectedRoute", "_ref", "children", "isAuthenticated", "loading", "location", "className", "size", "message", "to", "state", "from", "replace"], "sources": ["D:/ELGI/src/components/ProtectedRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { LoadingSpinner } from './common';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {\n  const { isAuthenticated, loading } = useAuth();\n  const location = useLocation();\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" message=\"Loading...\" />\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CACxD,OAASC,OAAO,KAAQ,yBAAyB,CACjD,OAASC,cAAc,KAAQ,UAAU,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAM1C,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjE,KAAM,CAAEE,eAAe,CAAEC,OAAQ,CAAC,CAAGV,OAAO,CAAC,CAAC,CAC9C,KAAM,CAAAW,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAE9B,GAAIW,OAAO,CAAE,CACX,mBACEP,IAAA,QAAKS,SAAS,CAAC,+CAA+C,CAAAJ,QAAA,cAC5DL,IAAA,CAACF,cAAc,EAACY,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,YAAY,CAAE,CAAC,CAC9C,CAAC,CAEV,CAEA,GAAI,CAACL,eAAe,CAAE,CACpB;AACA,mBAAON,IAAA,CAACL,QAAQ,EAACiB,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAEN,QAAS,CAAE,CAACO,OAAO,MAAE,CAAC,CACpE,CAEA,mBAAOf,IAAA,CAAAE,SAAA,EAAAG,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}