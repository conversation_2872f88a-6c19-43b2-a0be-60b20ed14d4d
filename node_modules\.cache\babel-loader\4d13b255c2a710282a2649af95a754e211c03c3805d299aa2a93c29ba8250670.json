{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport Layout from \"../Layout/Layout\";\nimport { apiService } from \"../../api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst sourceIcons = {\n  amazon: \"fab fa-amazon\",\n  ebay: \"fab fa-ebay\",\n  stripe: \"fab fa-stripe\",\n  paypal: \"fab fa-paypal\",\n  shopify: \"fab fa-shopify\",\n  default: \"fas fa-store\"\n};\nconst sourceColors = {\n  amazon: \"bg-yellow-100 text-yellow-600\",\n  ebay: \"bg-blue-100 text-blue-600\",\n  stripe: \"bg-indigo-100 text-indigo-600\",\n  paypal: \"bg-blue-50 text-blue-800\",\n  shopify: \"bg-green-100 text-green-600\",\n  default: \"bg-indigo-100 text-indigo-600\"\n};\nconst Dashboard = () => {\n  _s();\n  const [transactions, setTransactions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [statusCounts, setStatusCounts] = useState(null);\n  useEffect(() => {\n    const fetchTransactions = async () => {\n      try {\n        setLoading(true);\n        const data = await apiService.getDetails();\n        setTransactions(data);\n        const sourceMap = {};\n        data.forEach(t => {\n          sourceMap[t.source] = (sourceMap[t.source] || 0) + 1;\n        });\n        const sourcesArray = Object.entries(sourceMap).map(([source, total]) => ({\n          source,\n          total\n        }));\n        setSources(sourcesArray);\n      } catch (e) {\n        setError(\"Failed to load transaction data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchTransactions();\n  }, []);\n  useEffect(() => {\n    if (!selectedSource) {\n      setStatusCounts(null);\n      return;\n    }\n    const filtered = transactions.filter(t => t.source === selectedSource);\n    const counts = {\n      success: 0,\n      pending: 0,\n      failed: 0\n    };\n    filtered.forEach(t => {\n      const status = t.status.toLowerCase();\n      if (status === \"success\") counts.success += 1;else if (status === \"pending\") counts.pending += 1;else if (status === \"failed\") counts.failed += 1;\n    });\n    setStatusCounts(counts);\n  }, [selectedSource, transactions]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-4 border-t-transparent\",\n            style: {\n              borderColor: '#3579F3'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 font-medium\",\n            children: \"Loading dashboard...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-exclamation-triangle text-red-600 text-xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-600 font-medium\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-6\",\n        children: \"Transaction Sources\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6\",\n        children: sources.map(({\n          source,\n          total\n        }) => {\n          const lowerSource = source.toLowerCase();\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedSource(source),\n            className: `flex items-center p-4 rounded shadow cursor-pointer transition\n                  ${selectedSource === source ? \"border-2 border-indigo-600 bg-indigo-50\" : \"border border-gray-200 hover:shadow-md\"}`,\n            \"aria-pressed\": selectedSource === source,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 rounded mr-4 ${sourceColors[lowerSource] || sourceColors.default}`,\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: sourceIcons[lowerSource] || sourceIcons.default,\n                \"aria-hidden\": \"true\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-semibold capitalize\",\n                children: source\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: [total, \" transaction\", total !== 1 ? \"s\" : \"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, source, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), selectedSource && statusCounts && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-10 bg-white rounded shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-semibold mb-6 text-gray-900\",\n          children: [\"Status Breakdown for \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"capitalize\",\n            children: selectedSource\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"h-3 w-3 rounded-full bg-green-500 inline-block\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-700 font-semibold\",\n              children: \"Success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-auto text-green-800 font-bold\",\n              children: statusCounts.success\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"h-3 w-3 rounded-full bg-yellow-400 inline-block\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-yellow-700 font-semibold\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-auto text-yellow-800 font-bold\",\n              children: statusCounts.pending\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"h-3 w-3 rounded-full bg-red-500 inline-block\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-700 font-semibold\",\n              children: \"Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-auto text-red-800 font-bold\",\n              children: statusCounts.failed\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"ZS9tV5B/1AEWy5qcSmljjQ63bZg=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "apiService", "jsxDEV", "_jsxDEV", "sourceIcons", "amazon", "ebay", "stripe", "paypal", "shopify", "default", "sourceColors", "Dashboard", "_s", "transactions", "setTransactions", "loading", "setLoading", "error", "setError", "sources", "setSources", "selectedSource", "setSelectedSource", "statusCounts", "setStatusCounts", "fetchTransactions", "data", "getDetails", "sourceMap", "for<PERSON>ach", "t", "source", "sourcesArray", "Object", "entries", "map", "total", "e", "filtered", "filter", "counts", "success", "pending", "failed", "status", "toLowerCase", "children", "className", "style", "borderColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "lowerSource", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/Dashboard/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport Layout from \"../Layout/Layout\";\nimport { apiService } from \"../../api\";\n\ninterface Transaction {\n  id: number;\n  email: string;\n  details: any;\n  status: string;\n  reason: string | null;\n  source: string;\n  created_at: string;\n}\n\ninterface PlatformCount {\n  source: string;\n  total: number;\n}\n\ninterface StatusCounts {\n  success: number;\n  pending: number;\n  failed: number;\n}\n\nconst sourceIcons: Record<string, string> = {\n  amazon: \"fab fa-amazon\",\n  ebay: \"fab fa-ebay\",\n  stripe: \"fab fa-stripe\",\n  paypal: \"fab fa-paypal\",\n  shopify: \"fab fa-shopify\",\n  default: \"fas fa-store\",\n};\n\nconst sourceColors: Record<string, string> = {\n  amazon: \"bg-yellow-100 text-yellow-600\",\n  ebay: \"bg-blue-100 text-blue-600\",\n  stripe: \"bg-indigo-100 text-indigo-600\",\n  paypal: \"bg-blue-50 text-blue-800\",\n  shopify: \"bg-green-100 text-green-600\",\n  default: \"bg-indigo-100 text-indigo-600\",\n};\n\nconst Dashboard: React.FC = () => {\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string>(\"\");\n\n  const [sources, setSources] = useState<PlatformCount[]>([]);\n  const [selectedSource, setSelectedSource] = useState<string | null>(null);\n  const [statusCounts, setStatusCounts] = useState<StatusCounts | null>(null);\n\n  useEffect(() => {\n    const fetchTransactions = async () => {\n      try {\n        setLoading(true);\n        const data = await apiService.getDetails();\n        setTransactions(data);\n\n        const sourceMap: Record<string, number> = {};\n        data.forEach((t: Transaction) => {\n          sourceMap[t.source] = (sourceMap[t.source] || 0) + 1;\n        });\n        const sourcesArray = Object.entries(sourceMap).map(([source, total]) => ({ source, total }));\n        setSources(sourcesArray);\n      } catch (e) {\n        setError(\"Failed to load transaction data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchTransactions();\n  }, []);\n\n  useEffect(() => {\n    if (!selectedSource) {\n      setStatusCounts(null);\n      return;\n    }\n\n    const filtered = transactions.filter((t) => t.source === selectedSource);\n    const counts: StatusCounts = {\n      success: 0,\n      pending: 0,\n      failed: 0,\n    };\n    filtered.forEach((t) => {\n      const status = t.status.toLowerCase();\n      if (status === \"success\") counts.success += 1;\n      else if (status === \"pending\") counts.pending += 1;\n      else if (status === \"failed\") counts.failed += 1;\n    });\n    setStatusCounts(counts);\n  }, [selectedSource, transactions]);\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"flex justify-center items-center h-64\">\n          <div className=\"flex flex-col items-center space-y-4\">\n            <div\n              className=\"animate-spin rounded-full h-12 w-12 border-4 border-t-transparent\"\n              style={{ borderColor: '#3579F3' }}\n            />\n            <p className=\"text-gray-600 font-medium\">Loading dashboard...</p>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  if (error) {\n    return (\n      <Layout>\n        <div className=\"flex justify-center items-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <i className=\"fas fa-exclamation-triangle text-red-600 text-xl\" />\n            </div>\n            <p className=\"text-red-600 font-medium\">{error}</p>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"p-6 max-w-7xl mx-auto\">\n        <h1 className=\"text-3xl font-bold mb-6\">Transaction Sources</h1>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6\">\n          {sources.map(({ source, total }) => {\n            const lowerSource = source.toLowerCase();\n            return (\n              <button\n                key={source}\n                onClick={() => setSelectedSource(source)}\n                className={`flex items-center p-4 rounded shadow cursor-pointer transition\n                  ${\n                    selectedSource === source\n                      ? \"border-2 border-indigo-600 bg-indigo-50\"\n                      : \"border border-gray-200 hover:shadow-md\"\n                  }`}\n                aria-pressed={selectedSource === source}\n              >\n                <div className={`p-3 rounded mr-4 ${sourceColors[lowerSource] || sourceColors.default}`}>\n                  <i className={sourceIcons[lowerSource] || sourceIcons.default} aria-hidden=\"true\" />\n                </div>\n                <div>\n                  <p className=\"text-lg font-semibold capitalize\">{source}</p>\n                  <p className=\"text-gray-600\">\n                    {total} transaction{total !== 1 ? \"s\" : \"\"}\n                  </p>\n                </div>\n              </button>\n            );\n          })}\n        </div>\n\n        {/* Status counts for selected source */}\n        {selectedSource && statusCounts && (\n          <div className=\"mt-10 bg-white rounded shadow p-6\">\n            <h2 className=\"text-2xl font-semibold mb-6 text-gray-900\">\n              Status Breakdown for <span className=\"capitalize\">{selectedSource}</span>\n            </h2>\n            <div className=\"flex flex-col space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"h-3 w-3 rounded-full bg-green-500 inline-block\"></span>\n                <span className=\"text-green-700 font-semibold\">Success</span>\n                <span className=\"ml-auto text-green-800 font-bold\">{statusCounts.success}</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"h-3 w-3 rounded-full bg-yellow-400 inline-block\"></span>\n                <span className=\"text-yellow-700 font-semibold\">Pending</span>\n                <span className=\"ml-auto text-yellow-800 font-bold\">{statusCounts.pending}</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"h-3 w-3 rounded-full bg-red-500 inline-block\"></span>\n                <span className=\"text-red-700 font-semibold\">Failed</span>\n                <span className=\"ml-auto text-red-800 font-bold\">{statusCounts.failed}</span>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,UAAU,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuBvC,MAAMC,WAAmC,GAAG;EAC1CC,MAAM,EAAE,eAAe;EACvBC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,OAAO,EAAE,gBAAgB;EACzBC,OAAO,EAAE;AACX,CAAC;AAED,MAAMC,YAAoC,GAAG;EAC3CN,MAAM,EAAE,+BAA+B;EACvCC,IAAI,EAAE,2BAA2B;EACjCC,MAAM,EAAE,+BAA+B;EACvCC,MAAM,EAAE,0BAA0B;EAClCC,OAAO,EAAE,6BAA6B;EACtCC,OAAO,EAAE;AACX,CAAC;AAED,MAAME,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAS,EAAE,CAAC;EAE9C,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAkB,EAAE,CAAC;EAC3D,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAsB,IAAI,CAAC;EAE3EC,SAAS,CAAC,MAAM;IACd,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFT,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMU,IAAI,GAAG,MAAM1B,UAAU,CAAC2B,UAAU,CAAC,CAAC;QAC1Cb,eAAe,CAACY,IAAI,CAAC;QAErB,MAAME,SAAiC,GAAG,CAAC,CAAC;QAC5CF,IAAI,CAACG,OAAO,CAAEC,CAAc,IAAK;UAC/BF,SAAS,CAACE,CAAC,CAACC,MAAM,CAAC,GAAG,CAACH,SAAS,CAACE,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACtD,CAAC,CAAC;QACF,MAAMC,YAAY,GAAGC,MAAM,CAACC,OAAO,CAACN,SAAS,CAAC,CAACO,GAAG,CAAC,CAAC,CAACJ,MAAM,EAAEK,KAAK,CAAC,MAAM;UAAEL,MAAM;UAAEK;QAAM,CAAC,CAAC,CAAC;QAC5FhB,UAAU,CAACY,YAAY,CAAC;MAC1B,CAAC,CAAC,OAAOK,CAAC,EAAE;QACVnB,QAAQ,CAAC,iCAAiC,CAAC;MAC7C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDS,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN3B,SAAS,CAAC,MAAM;IACd,IAAI,CAACuB,cAAc,EAAE;MACnBG,eAAe,CAAC,IAAI,CAAC;MACrB;IACF;IAEA,MAAMc,QAAQ,GAAGzB,YAAY,CAAC0B,MAAM,CAAET,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAKV,cAAc,CAAC;IACxE,MAAMmB,MAAoB,GAAG;MAC3BC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE;IACV,CAAC;IACDL,QAAQ,CAACT,OAAO,CAAEC,CAAC,IAAK;MACtB,MAAMc,MAAM,GAAGd,CAAC,CAACc,MAAM,CAACC,WAAW,CAAC,CAAC;MACrC,IAAID,MAAM,KAAK,SAAS,EAAEJ,MAAM,CAACC,OAAO,IAAI,CAAC,CAAC,KACzC,IAAIG,MAAM,KAAK,SAAS,EAAEJ,MAAM,CAACE,OAAO,IAAI,CAAC,CAAC,KAC9C,IAAIE,MAAM,KAAK,QAAQ,EAAEJ,MAAM,CAACG,MAAM,IAAI,CAAC;IAClD,CAAC,CAAC;IACFnB,eAAe,CAACgB,MAAM,CAAC;EACzB,CAAC,EAAE,CAACnB,cAAc,EAAER,YAAY,CAAC,CAAC;EAElC,IAAIE,OAAO,EAAE;IACX,oBACEb,OAAA,CAACH,MAAM;MAAA+C,QAAA,eACL5C,OAAA;QAAK6C,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpD5C,OAAA;UAAK6C,SAAS,EAAC,sCAAsC;UAAAD,QAAA,gBACnD5C,OAAA;YACE6C,SAAS,EAAC,mEAAmE;YAC7EC,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFnD,OAAA;YAAG6C,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EAAC;UAAoB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,IAAIpC,KAAK,EAAE;IACT,oBACEf,OAAA,CAACH,MAAM;MAAA+C,QAAA,eACL5C,OAAA;QAAK6C,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpD5C,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1B5C,OAAA;YAAK6C,SAAS,EAAC,iFAAiF;YAAAD,QAAA,eAC9F5C,OAAA;cAAG6C,SAAS,EAAC;YAAkD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNnD,OAAA;YAAG6C,SAAS,EAAC,0BAA0B;YAAAD,QAAA,EAAE7B;UAAK;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACEnD,OAAA,CAACH,MAAM;IAAA+C,QAAA,eACL5C,OAAA;MAAK6C,SAAS,EAAC,uBAAuB;MAAAD,QAAA,gBACpC5C,OAAA;QAAI6C,SAAS,EAAC,yBAAyB;QAAAD,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChEnD,OAAA;QAAK6C,SAAS,EAAC,sDAAsD;QAAAD,QAAA,EAClE3B,OAAO,CAACgB,GAAG,CAAC,CAAC;UAAEJ,MAAM;UAAEK;QAAM,CAAC,KAAK;UAClC,MAAMkB,WAAW,GAAGvB,MAAM,CAACc,WAAW,CAAC,CAAC;UACxC,oBACE3C,OAAA;YAEEqD,OAAO,EAAEA,CAAA,KAAMjC,iBAAiB,CAACS,MAAM,CAAE;YACzCgB,SAAS,EAAE;AAC3B,oBACoB1B,cAAc,KAAKU,MAAM,GACrB,yCAAyC,GACzC,wCAAwC,EAC3C;YACL,gBAAcV,cAAc,KAAKU,MAAO;YAAAe,QAAA,gBAExC5C,OAAA;cAAK6C,SAAS,EAAE,oBAAoBrC,YAAY,CAAC4C,WAAW,CAAC,IAAI5C,YAAY,CAACD,OAAO,EAAG;cAAAqC,QAAA,eACtF5C,OAAA;gBAAG6C,SAAS,EAAE5C,WAAW,CAACmD,WAAW,CAAC,IAAInD,WAAW,CAACM,OAAQ;gBAAC,eAAY;cAAM;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eACNnD,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAG6C,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAEf;cAAM;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DnD,OAAA;gBAAG6C,SAAS,EAAC,eAAe;gBAAAD,QAAA,GACzBV,KAAK,EAAC,cAAY,EAACA,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;cAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAlBDtB,MAAM;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBL,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLhC,cAAc,IAAIE,YAAY,iBAC7BrB,OAAA;QAAK6C,SAAS,EAAC,mCAAmC;QAAAD,QAAA,gBAChD5C,OAAA;UAAI6C,SAAS,EAAC,2CAA2C;UAAAD,QAAA,GAAC,uBACnC,eAAA5C,OAAA;YAAM6C,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAEzB;UAAc;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACLnD,OAAA;UAAK6C,SAAS,EAAC,yBAAyB;UAAAD,QAAA,gBACtC5C,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C5C,OAAA;cAAM6C,SAAS,EAAC;YAAgD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxEnD,OAAA;cAAM6C,SAAS,EAAC,8BAA8B;cAAAD,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7DnD,OAAA;cAAM6C,SAAS,EAAC,kCAAkC;cAAAD,QAAA,EAAEvB,YAAY,CAACkB;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACNnD,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C5C,OAAA;cAAM6C,SAAS,EAAC;YAAiD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzEnD,OAAA;cAAM6C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DnD,OAAA;cAAM6C,SAAS,EAAC,mCAAmC;cAAAD,QAAA,EAAEvB,YAAY,CAACmB;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACNnD,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C5C,OAAA;cAAM6C,SAAS,EAAC;YAA8C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtEnD,OAAA;cAAM6C,SAAS,EAAC,4BAA4B;cAAAD,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DnD,OAAA;cAAM6C,SAAS,EAAC,gCAAgC;cAAAD,QAAA,EAAEvB,YAAY,CAACoB;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACzC,EAAA,CAhJID,SAAmB;AAAA6C,EAAA,GAAnB7C,SAAmB;AAkJzB,eAAeA,SAAS;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}