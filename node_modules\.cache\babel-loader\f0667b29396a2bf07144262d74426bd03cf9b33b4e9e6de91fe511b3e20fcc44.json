{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport Layout from \"../Layout/Layout\";\nimport { apiService } from \"../../api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Icon mapping for each source (FontAwesome classes)\nconst sourceIcons = {\n  amazon: \"fab fa-amazon\",\n  ebay: \"fab fa-ebay\",\n  stripe: \"fab fa-stripe\",\n  paypal: \"fab fa-paypal\",\n  shopify: \"fab fa-shopify\",\n  default: \"fas fa-store\"\n};\n\n// Color mapping for icon backgrounds and text\nconst sourceColors = {\n  amazon: \"bg-yellow-100 text-yellow-600\",\n  ebay: \"bg-blue-100 text-blue-600\",\n  stripe: \"bg-indigo-100 text-indigo-600\",\n  paypal: \"bg-blue-50 text-blue-800\",\n  shopify: \"bg-green-100 text-green-600\",\n  default: \"bg-indigo-100 text-indigo-600\"\n};\nconst Dashboard = () => {\n  _s();\n  const [transactions, setTransactions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [statusCounts, setStatusCounts] = useState(null);\n\n  // Fetch all transactions on mount\n  useEffect(() => {\n    const fetchTransactions = async () => {\n      try {\n        setLoading(true);\n        setError(\"\");\n        const data = await apiService.getDetails();\n        setTransactions(data);\n\n        // Compute unique sources with counts\n        const sourceMap = {};\n        data.forEach(t => {\n          sourceMap[t.source] = (sourceMap[t.source] || 0) + 1;\n        });\n        const sourcesArray = Object.entries(sourceMap).map(([source, total]) => ({\n          source,\n          total\n        }));\n        setSources(sourcesArray);\n\n        // Reset selected source and status counts on reload\n        setSelectedSource(null);\n        setStatusCounts(null);\n      } catch (e) {\n        console.error(\"Failed to fetch transactions\", e);\n        setError(\"Failed to load transaction data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchTransactions();\n  }, []);\n\n  // When selectedSource changes, compute status counts\n  useEffect(() => {\n    if (!selectedSource) {\n      setStatusCounts(null);\n      return;\n    }\n    // Filter transactions by selected source\n    const filtered = transactions.filter(t => t.source === selectedSource);\n    const counts = {\n      success: 0,\n      pending: 0,\n      failed: 0\n    };\n    filtered.forEach(t => {\n      const status = t.status.toLowerCase();\n      if (status === \"success\") counts.success += 1;else if (status === \"pending\") counts.pending += 1;else if (status === \"failed\") counts.failed += 1;\n    });\n    setStatusCounts(counts);\n  }, [selectedSource, transactions]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col justify-center items-center h-64 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-600 text-lg\",\n          children: \"Loading transactions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-red-600 mt-20 text-xl font-semibold\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-8 max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-extrabold mb-8 text-gray-900 tracking-tight\",\n        children: \"Transaction Sources\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), sources.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-center text-gray-500 text-lg\",\n        children: \"No transaction sources found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8\",\n        children: sources.map(({\n          source,\n          total\n        }) => {\n          const lowerSource = source.toLowerCase();\n          const isSelected = selectedSource === source;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setSelectedSource(source),\n            className: `flex items-center p-5 rounded-lg shadow-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 ${isSelected ? \"border-2 border-indigo-600 bg-indigo-50\" : \"border border-gray-200 hover:shadow-lg hover:border-indigo-400\"}`,\n            \"aria-pressed\": isSelected,\n            \"aria-label\": `Filter transactions by ${source}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex justify-center items-center p-4 rounded-full mr-5 flex-shrink-0 ${sourceColors[lowerSource] || sourceColors.default}`,\n              \"aria-hidden\": \"true\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `${sourceIcons[lowerSource] || sourceIcons.default} text-2xl`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl font-semibold capitalize text-gray-800\",\n                children: source\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mt-1 text-sm\",\n                children: [total, \" transaction\", total !== 1 ? \"s\" : \"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 19\n            }, this)]\n          }, source, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this), selectedSource && statusCounts && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-14\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-semibold mb-6 text-gray-900\",\n          children: [\"Status Breakdown for \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"capitalize\",\n            children: selectedSource\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-[150px] bg-green-50 p-6 rounded-lg shadow flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-700 font-extrabold text-4xl\",\n              children: statusCounts.success\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"uppercase text-green-700 tracking-wider mt-2 font-medium text-sm\",\n              children: \"Success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-[150px] bg-yellow-50 p-6 rounded-lg shadow flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-yellow-700 font-extrabold text-4xl\",\n              children: statusCounts.pending\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"uppercase text-yellow-700 tracking-wider mt-2 font-medium text-sm\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-[150px] bg-red-50 p-6 rounded-lg shadow flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-700 font-extrabold text-4xl\",\n              children: statusCounts.failed\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"uppercase text-red-700 tracking-wider mt-2 font-medium text-sm\",\n              children: \"Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"ZS9tV5B/1AEWy5qcSmljjQ63bZg=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "apiService", "jsxDEV", "_jsxDEV", "sourceIcons", "amazon", "ebay", "stripe", "paypal", "shopify", "default", "sourceColors", "Dashboard", "_s", "transactions", "setTransactions", "loading", "setLoading", "error", "setError", "sources", "setSources", "selectedSource", "setSelectedSource", "statusCounts", "setStatusCounts", "fetchTransactions", "data", "getDetails", "sourceMap", "for<PERSON>ach", "t", "source", "sourcesArray", "Object", "entries", "map", "total", "e", "console", "filtered", "filter", "counts", "success", "pending", "failed", "status", "toLowerCase", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "lowerSource", "isSelected", "type", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/Dashboard/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport Layout from \"../Layout/Layout\";\nimport { apiService } from \"../../api\";\n\ninterface Transaction {\n  id: number;\n  email: string;\n  details: any;\n  status: string;\n  reason: string | null;\n  source: string;\n  created_at: string;\n}\n\ninterface PlatformCount {\n  source: string;\n  total: number;\n}\n\ninterface StatusCounts {\n  success: number;\n  pending: number;\n  failed: number;\n}\n\n// Icon mapping for each source (FontAwesome classes)\nconst sourceIcons: Record<string, string> = {\n  amazon: \"fab fa-amazon\",\n  ebay: \"fab fa-ebay\",\n  stripe: \"fab fa-stripe\",\n  paypal: \"fab fa-paypal\",\n  shopify: \"fab fa-shopify\",\n  default: \"fas fa-store\",\n};\n\n// Color mapping for icon backgrounds and text\nconst sourceColors: Record<string, string> = {\n  amazon: \"bg-yellow-100 text-yellow-600\",\n  ebay: \"bg-blue-100 text-blue-600\",\n  stripe: \"bg-indigo-100 text-indigo-600\",\n  paypal: \"bg-blue-50 text-blue-800\",\n  shopify: \"bg-green-100 text-green-600\",\n  default: \"bg-indigo-100 text-indigo-600\",\n};\n\nconst Dashboard: React.FC = () => {\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string>(\"\");\n\n  const [sources, setSources] = useState<PlatformCount[]>([]);\n  const [selectedSource, setSelectedSource] = useState<string | null>(null);\n  const [statusCounts, setStatusCounts] = useState<StatusCounts | null>(null);\n\n  // Fetch all transactions on mount\n  useEffect(() => {\n    const fetchTransactions = async () => {\n      try {\n        setLoading(true);\n        setError(\"\");\n        const data = await apiService.getDetails();\n        setTransactions(data);\n\n        // Compute unique sources with counts\n        const sourceMap: Record<string, number> = {};\n        data.forEach((t: Transaction) => {\n          sourceMap[t.source] = (sourceMap[t.source] || 0) + 1;\n        });\n        const sourcesArray = Object.entries(sourceMap).map(([source, total]) => ({\n          source,\n          total,\n        }));\n        setSources(sourcesArray);\n\n        // Reset selected source and status counts on reload\n        setSelectedSource(null);\n        setStatusCounts(null);\n      } catch (e) {\n        console.error(\"Failed to fetch transactions\", e);\n        setError(\"Failed to load transaction data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchTransactions();\n  }, []);\n\n  // When selectedSource changes, compute status counts\n  useEffect(() => {\n    if (!selectedSource) {\n      setStatusCounts(null);\n      return;\n    }\n    // Filter transactions by selected source\n    const filtered = transactions.filter((t) => t.source === selectedSource);\n    const counts: StatusCounts = {\n      success: 0,\n      pending: 0,\n      failed: 0,\n    };\n    filtered.forEach((t) => {\n      const status = t.status.toLowerCase();\n      if (status === \"success\") counts.success += 1;\n      else if (status === \"pending\") counts.pending += 1;\n      else if (status === \"failed\") counts.failed += 1;\n    });\n    setStatusCounts(counts);\n  }, [selectedSource, transactions]);\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"flex flex-col justify-center items-center h-64 space-y-3\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\" />\n          <span className=\"text-gray-600 text-lg\">Loading transactions...</span>\n        </div>\n      </Layout>\n    );\n  }\n\n  if (error) {\n    return (\n      <Layout>\n        <div className=\"text-center text-red-600 mt-20 text-xl font-semibold\">{error}</div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"p-8 max-w-7xl mx-auto\">\n        <h1 className=\"text-4xl font-extrabold mb-8 text-gray-900 tracking-tight\">Transaction Sources</h1>\n        {sources.length === 0 ? (\n          <p className=\"text-center text-gray-500 text-lg\">No transaction sources found.</p>\n        ) : (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8\">\n            {sources.map(({ source, total }) => {\n              const lowerSource = source.toLowerCase();\n              const isSelected = selectedSource === source;\n              return (\n                <button\n                  key={source}\n                  type=\"button\"\n                  onClick={() => setSelectedSource(source)}\n                  className={`flex items-center p-5 rounded-lg shadow-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 ${\n                    isSelected\n                      ? \"border-2 border-indigo-600 bg-indigo-50\"\n                      : \"border border-gray-200 hover:shadow-lg hover:border-indigo-400\"\n                  }`}\n                  aria-pressed={isSelected}\n                  aria-label={`Filter transactions by ${source}`}\n                >\n                  <div\n                    className={`flex justify-center items-center p-4 rounded-full mr-5 flex-shrink-0 ${\n                      sourceColors[lowerSource] || sourceColors.default\n                    }`}\n                    aria-hidden=\"true\"\n                  >\n                    <i className={`${sourceIcons[lowerSource] || sourceIcons.default} text-2xl`} />\n                  </div>\n                  <div className=\"text-left\">\n                    <p className=\"text-xl font-semibold capitalize text-gray-800\">{source}</p>\n                    <p className=\"text-gray-500 mt-1 text-sm\">\n                      {total} transaction{total !== 1 ? \"s\" : \"\"}\n                    </p>\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        )}\n\n        {/* Status counts for selected source */}\n        {selectedSource && statusCounts && (\n          <section className=\"mt-14\">\n            <h2 className=\"text-3xl font-semibold mb-6 text-gray-900\">\n              Status Breakdown for <span className=\"capitalize\">{selectedSource}</span>\n            </h2>\n            <div className=\"flex flex-wrap gap-8\">\n              <div className=\"flex-1 min-w-[150px] bg-green-50 p-6 rounded-lg shadow flex flex-col items-center\">\n                <span className=\"text-green-700 font-extrabold text-4xl\">{statusCounts.success}</span>\n                <span className=\"uppercase text-green-700 tracking-wider mt-2 font-medium text-sm\">Success</span>\n              </div>\n              <div className=\"flex-1 min-w-[150px] bg-yellow-50 p-6 rounded-lg shadow flex flex-col items-center\">\n                <span className=\"text-yellow-700 font-extrabold text-4xl\">{statusCounts.pending}</span>\n                <span className=\"uppercase text-yellow-700 tracking-wider mt-2 font-medium text-sm\">Pending</span>\n              </div>\n              <div className=\"flex-1 min-w-[150px] bg-red-50 p-6 rounded-lg shadow flex flex-col items-center\">\n                <span className=\"text-red-700 font-extrabold text-4xl\">{statusCounts.failed}</span>\n                <span className=\"uppercase text-red-700 tracking-wider mt-2 font-medium text-sm\">Failed</span>\n              </div>\n            </div>\n          </section>\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,UAAU,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuBvC;AACA,MAAMC,WAAmC,GAAG;EAC1CC,MAAM,EAAE,eAAe;EACvBC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,OAAO,EAAE,gBAAgB;EACzBC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,MAAMC,YAAoC,GAAG;EAC3CN,MAAM,EAAE,+BAA+B;EACvCC,IAAI,EAAE,2BAA2B;EACjCC,MAAM,EAAE,+BAA+B;EACvCC,MAAM,EAAE,0BAA0B;EAClCC,OAAO,EAAE,6BAA6B;EACtCC,OAAO,EAAE;AACX,CAAC;AAED,MAAME,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAS,EAAE,CAAC;EAE9C,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAkB,EAAE,CAAC;EAC3D,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAsB,IAAI,CAAC;;EAE3E;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFT,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,EAAE,CAAC;QACZ,MAAMQ,IAAI,GAAG,MAAM1B,UAAU,CAAC2B,UAAU,CAAC,CAAC;QAC1Cb,eAAe,CAACY,IAAI,CAAC;;QAErB;QACA,MAAME,SAAiC,GAAG,CAAC,CAAC;QAC5CF,IAAI,CAACG,OAAO,CAAEC,CAAc,IAAK;UAC/BF,SAAS,CAACE,CAAC,CAACC,MAAM,CAAC,GAAG,CAACH,SAAS,CAACE,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACtD,CAAC,CAAC;QACF,MAAMC,YAAY,GAAGC,MAAM,CAACC,OAAO,CAACN,SAAS,CAAC,CAACO,GAAG,CAAC,CAAC,CAACJ,MAAM,EAAEK,KAAK,CAAC,MAAM;UACvEL,MAAM;UACNK;QACF,CAAC,CAAC,CAAC;QACHhB,UAAU,CAACY,YAAY,CAAC;;QAExB;QACAV,iBAAiB,CAAC,IAAI,CAAC;QACvBE,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,CAAC,OAAOa,CAAC,EAAE;QACVC,OAAO,CAACrB,KAAK,CAAC,8BAA8B,EAAEoB,CAAC,CAAC;QAChDnB,QAAQ,CAAC,iCAAiC,CAAC;MAC7C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDS,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3B,SAAS,CAAC,MAAM;IACd,IAAI,CAACuB,cAAc,EAAE;MACnBG,eAAe,CAAC,IAAI,CAAC;MACrB;IACF;IACA;IACA,MAAMe,QAAQ,GAAG1B,YAAY,CAAC2B,MAAM,CAAEV,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAKV,cAAc,CAAC;IACxE,MAAMoB,MAAoB,GAAG;MAC3BC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE;IACV,CAAC;IACDL,QAAQ,CAACV,OAAO,CAAEC,CAAC,IAAK;MACtB,MAAMe,MAAM,GAAGf,CAAC,CAACe,MAAM,CAACC,WAAW,CAAC,CAAC;MACrC,IAAID,MAAM,KAAK,SAAS,EAAEJ,MAAM,CAACC,OAAO,IAAI,CAAC,CAAC,KACzC,IAAIG,MAAM,KAAK,SAAS,EAAEJ,MAAM,CAACE,OAAO,IAAI,CAAC,CAAC,KAC9C,IAAIE,MAAM,KAAK,QAAQ,EAAEJ,MAAM,CAACG,MAAM,IAAI,CAAC;IAClD,CAAC,CAAC;IACFpB,eAAe,CAACiB,MAAM,CAAC;EACzB,CAAC,EAAE,CAACpB,cAAc,EAAER,YAAY,CAAC,CAAC;EAElC,IAAIE,OAAO,EAAE;IACX,oBACEb,OAAA,CAACH,MAAM;MAAAgD,QAAA,eACL7C,OAAA;QAAK8C,SAAS,EAAC,0DAA0D;QAAAD,QAAA,gBACvE7C,OAAA;UAAK8C,SAAS,EAAC;QAA6E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/FlD,OAAA;UAAM8C,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,IAAInC,KAAK,EAAE;IACT,oBACEf,OAAA,CAACH,MAAM;MAAAgD,QAAA,eACL7C,OAAA;QAAK8C,SAAS,EAAC,sDAAsD;QAAAD,QAAA,EAAE9B;MAAK;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CAAC;EAEb;EAEA,oBACElD,OAAA,CAACH,MAAM;IAAAgD,QAAA,eACL7C,OAAA;MAAK8C,SAAS,EAAC,uBAAuB;MAAAD,QAAA,gBACpC7C,OAAA;QAAI8C,SAAS,EAAC,2DAA2D;QAAAD,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACjGjC,OAAO,CAACkC,MAAM,KAAK,CAAC,gBACnBnD,OAAA;QAAG8C,SAAS,EAAC,mCAAmC;QAAAD,QAAA,EAAC;MAA6B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAElFlD,OAAA;QAAK8C,SAAS,EAAC,sDAAsD;QAAAD,QAAA,EAClE5B,OAAO,CAACgB,GAAG,CAAC,CAAC;UAAEJ,MAAM;UAAEK;QAAM,CAAC,KAAK;UAClC,MAAMkB,WAAW,GAAGvB,MAAM,CAACe,WAAW,CAAC,CAAC;UACxC,MAAMS,UAAU,GAAGlC,cAAc,KAAKU,MAAM;UAC5C,oBACE7B,OAAA;YAEEsD,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAEA,CAAA,KAAMnC,iBAAiB,CAACS,MAAM,CAAE;YACzCiB,SAAS,EAAE,mIACTO,UAAU,GACN,yCAAyC,GACzC,gEAAgE,EACnE;YACH,gBAAcA,UAAW;YACzB,cAAY,0BAA0BxB,MAAM,EAAG;YAAAgB,QAAA,gBAE/C7C,OAAA;cACE8C,SAAS,EAAE,wEACTtC,YAAY,CAAC4C,WAAW,CAAC,IAAI5C,YAAY,CAACD,OAAO,EAChD;cACH,eAAY,MAAM;cAAAsC,QAAA,eAElB7C,OAAA;gBAAG8C,SAAS,EAAE,GAAG7C,WAAW,CAACmD,WAAW,CAAC,IAAInD,WAAW,CAACM,OAAO;cAAY;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eACNlD,OAAA;cAAK8C,SAAS,EAAC,WAAW;cAAAD,QAAA,gBACxB7C,OAAA;gBAAG8C,SAAS,EAAC,gDAAgD;gBAAAD,QAAA,EAAEhB;cAAM;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1ElD,OAAA;gBAAG8C,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,GACtCX,KAAK,EAAC,cAAY,EAACA,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAxBDrB,MAAM;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBL,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGA/B,cAAc,IAAIE,YAAY,iBAC7BrB,OAAA;QAAS8C,SAAS,EAAC,OAAO;QAAAD,QAAA,gBACxB7C,OAAA;UAAI8C,SAAS,EAAC,2CAA2C;UAAAD,QAAA,GAAC,uBACnC,eAAA7C,OAAA;YAAM8C,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAE1B;UAAc;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACLlD,OAAA;UAAK8C,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACnC7C,OAAA;YAAK8C,SAAS,EAAC,mFAAmF;YAAAD,QAAA,gBAChG7C,OAAA;cAAM8C,SAAS,EAAC,wCAAwC;cAAAD,QAAA,EAAExB,YAAY,CAACmB;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtFlD,OAAA;cAAM8C,SAAS,EAAC,kEAAkE;cAAAD,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,oFAAoF;YAAAD,QAAA,gBACjG7C,OAAA;cAAM8C,SAAS,EAAC,yCAAyC;cAAAD,QAAA,EAAExB,YAAY,CAACoB;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvFlD,OAAA;cAAM8C,SAAS,EAAC,mEAAmE;cAAAD,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,iFAAiF;YAAAD,QAAA,gBAC9F7C,OAAA;cAAM8C,SAAS,EAAC,sCAAsC;cAAAD,QAAA,EAAExB,YAAY,CAACqB;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnFlD,OAAA;cAAM8C,SAAS,EAAC,gEAAgE;cAAAD,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACxC,EAAA,CAxJID,SAAmB;AAAA+C,EAAA,GAAnB/C,SAAmB;AA0JzB,eAAeA,SAAS;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}