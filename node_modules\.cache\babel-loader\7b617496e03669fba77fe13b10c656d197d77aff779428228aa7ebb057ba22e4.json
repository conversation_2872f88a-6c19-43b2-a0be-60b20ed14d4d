{"ast": null, "code": "import React,{useState}from'react';import{Navigate,useLocation}from'react-router-dom';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Login=()=>{const{login,isAuthenticated}=useAuth();const location=useLocation();const[formData,setFormData]=useState({username:'',password:''});const[error,setError]=useState('');const[isLoading,setIsLoading]=useState(false);// Redirect if already authenticated\nif(isAuthenticated){var _location$state,_location$state$from;const from=((_location$state=location.state)===null||_location$state===void 0?void 0:(_location$state$from=_location$state.from)===null||_location$state$from===void 0?void 0:_location$state$from.pathname)||'/dashboard';return/*#__PURE__*/_jsx(Navigate,{to:from,replace:true});}const handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>({...prev,[name]:value}));if(error)setError('');};const handleSubmit=async e=>{e.preventDefault();setIsLoading(true);setError('');// Client-side validation for exact credentials\nif(formData.username!=='admin'){setError('Invalid username. Please enter \"admin\".');setIsLoading(false);return;}if(formData.password!=='admin@123'){setError('Invalid password. Please enter \"admin@123\".');setIsLoading(false);return;}try{const result=login(formData);if(!result.success){setError(result.message||'Login failed');}}catch(err){setError('An unexpected error occurred');}finally{setIsLoading(false);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex flex-col justify-center px-12 lg:px-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md w-full mx-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-12 text-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6 mx-auto\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"https://www.elgi.com/sg/wp-content/themes/ELGi/images/elgi__logo.png\",alt:\"ELGi Logo\",className:\"h-16 mx-auto\",onError:e=>{// Fallback to text if image fails to load\ne.currentTarget.style.display='none';const fallback=e.currentTarget.nextElementSibling;if(fallback)fallback.style.display='block';}}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden text-2xl font-bold text-gray-900\",children:\"ELGi\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-gray-900 mb-2\",children:\"Welcome Back\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Please sign in to your account to continue\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"username\",className:\"block text-sm font-semibold text-gray-700 mb-3\",children:\"Username\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user text-gray-400\"})}),/*#__PURE__*/_jsx(\"input\",{id:\"username\",name:\"username\",type:\"text\",placeholder:\"admin\",value:formData.username,onChange:handleInputChange,className:\"w-full pl-10 pr-4 py-4 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",required:true,disabled:isLoading})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",className:\"block text-sm font-semibold text-gray-700 mb-3\",children:\"Password\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-lock text-gray-400\"})}),/*#__PURE__*/_jsx(\"input\",{id:\"password\",name:\"password\",type:\"password\",placeholder:\"admin@123\",value:formData.password,onChange:handleInputChange,className:\"w-full pl-10 pr-4 py-4 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",required:true,disabled:isLoading})]})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 text-red-600 text-sm bg-red-50 border border-red-200 rounded-xl p-4\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-exclamation-circle\"}),/*#__PURE__*/_jsx(\"span\",{children:error})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isLoading,className:\"w-full bg-blue-600 hover:bg-blue-700 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg hover:shadow-xl\",children:isLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3\"}),\"Signing In...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sign-in-alt mr-2\"}),\"Sign In\"]})})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden lg:flex lg:flex-1 bg-gradient-to-br from-blue-600 to-indigo-700 flex-col justify-center items-center px-12 text-white relative overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-black opacity-10\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative z-10 max-w-md text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chart-line text-3xl\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-4xl font-bold mb-4\",children:\"Welcome Back!\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-lg leading-relaxed\",children:\"Access your admin dashboard to manage transactions, monitor performance, and stay in control of your business operations.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-3 gap-4 mt-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shield-alt text-xl\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-blue-100\",children:\"Secure\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-tachometer-alt text-xl\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-blue-100\",children:\"Fast\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-users text-xl\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-blue-100\",children:\"Reliable\"})]})]})]})]})]});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "Navigate", "useLocation", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON><PERSON>", "login", "isAuthenticated", "location", "formData", "setFormData", "username", "password", "error", "setError", "isLoading", "setIsLoading", "_location$state", "_location$state$from", "from", "state", "pathname", "to", "replace", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "result", "success", "message", "err", "className", "children", "src", "alt", "onError", "currentTarget", "style", "display", "fallback", "nextElement<PERSON><PERSON>ling", "onSubmit", "htmlFor", "id", "type", "placeholder", "onChange", "required", "disabled"], "sources": ["D:/ELGI/src/components/Login/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { LoginCredentials } from '../../api';\n\nconst Login: React.FC = () => {\n  const { login, isAuthenticated } = useAuth();\n  const location = useLocation();\n  \n  const [formData, setFormData] = useState<LoginCredentials>({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState<string>('');\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n\n  // Redirect if already authenticated\n  if (isAuthenticated) {\n    const from = location.state?.from?.pathname || '/dashboard';\n    return <Navigate to={from} replace />;\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    // Client-side validation for exact credentials\n    if (formData.username !== 'admin') {\n      setError('Invalid username. Please enter \"admin\".');\n      setIsLoading(false);\n      return;\n    }\n\n    if (formData.password !== 'admin@123') {\n      setError('Invalid password. Please enter \"admin@123\".');\n      setIsLoading(false);\n      return;\n    }\n\n    try {\n      const result = login(formData);\n\n      if (!result.success) {\n        setError(result.message || 'Login failed');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex\">\n      <div className=\"flex-1 flex flex-col justify-center px-12 lg:px-20\">\n        <div className=\"max-w-md w-full mx-auto\">\n          <div className=\"mb-12 text-center\">\n            <div className=\"mb-6 mx-auto\">\n              <img\n                src=\"https://www.elgi.com/sg/wp-content/themes/ELGi/images/elgi__logo.png\"\n                alt=\"ELGi Logo\"\n                className=\"h-16 mx-auto\"\n                onError={(e) => {\n                  // Fallback to text if image fails to load\n                  e.currentTarget.style.display = 'none';\n                  const fallback = e.currentTarget.nextElementSibling as HTMLElement;\n                  if (fallback) fallback.style.display = 'block';\n                }}\n              />\n              <div className=\"hidden text-2xl font-bold text-gray-900\">\n                ELGi\n              </div>\n            </div>\n            {/* <h1 className=\"text-3xl font-bold text-gray-900\">ELGi Dashboard</h1>\n            <p className=\"text-gray-600 mt-2\">Admin Portal</p> */}\n          </div>\n\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">Welcome Back</h2>\n              <p className=\"text-gray-600\">\n                Please sign in to your account to continue\n              </p>\n            </div>\n\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"username\" className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                  Username\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <i className=\"fas fa-user text-gray-400\"></i>\n                  </div>\n                  <input\n                    id=\"username\"\n                    name=\"username\"\n                    type=\"text\"\n                    placeholder=\"admin\"\n                    value={formData.username}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-4 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    required\n                    disabled={isLoading}\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <i className=\"fas fa-lock text-gray-400\"></i>\n                  </div>\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type=\"password\"\n                    placeholder=\"admin@123\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-4 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    required\n                    disabled={isLoading}\n                  />\n                </div>\n              </div>\n\n              {error && (\n                <div className=\"flex items-center space-x-2 text-red-600 text-sm bg-red-50 border border-red-200 rounded-xl p-4\">\n                  <i className=\"fas fa-exclamation-circle\"></i>\n                  <span>{error}</span>\n                </div>\n              )}\n\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full bg-blue-600 hover:bg-blue-700 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg hover:shadow-xl\"\n              >\n                {isLoading ? (\n                  <>\n                    <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3\"></div>\n                    Signing In...\n                  </>\n                ) : (\n                  <>\n                    <i className=\"fas fa-sign-in-alt mr-2\"></i>\n                    Sign In\n                  </>\n                )}\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"hidden lg:flex lg:flex-1 bg-gradient-to-br from-blue-600 to-indigo-700 flex-col justify-center items-center px-12 text-white relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-black opacity-10\"></div>\n        <div className=\"relative z-10 max-w-md text-center\">\n          <div className=\"mb-8\">\n            <div className=\"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <i className=\"fas fa-chart-line text-3xl\"></i>\n            </div>\n            <h2 className=\"text-4xl font-bold mb-4\">Welcome Back!</h2>\n            <p className=\"text-blue-100 text-lg leading-relaxed\">\n              Access your admin dashboard to manage transactions, monitor performance, and stay in control of your business operations.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-3 gap-4 mt-8\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\">\n                <i className=\"fas fa-shield-alt text-xl\"></i>\n              </div>\n              <p className=\"text-sm text-blue-100\">Secure</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\">\n                <i className=\"fas fa-tachometer-alt text-xl\"></i>\n              </div>\n              <p className=\"text-sm text-blue-100\">Fast</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2\">\n                <i className=\"fas fa-users text-xl\"></i>\n              </div>\n              <p className=\"text-sm text-blue-100\">Reliable</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CACxD,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGrD,KAAM,CAAAC,KAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAEC,KAAK,CAAEC,eAAgB,CAAC,CAAGT,OAAO,CAAC,CAAC,CAC5C,KAAM,CAAAU,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACY,QAAQ,CAAEC,WAAW,CAAC,CAAGf,QAAQ,CAAmB,CACzDgB,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACoB,SAAS,CAAEC,YAAY,CAAC,CAAGrB,QAAQ,CAAU,KAAK,CAAC,CAE1D;AACA,GAAIY,eAAe,CAAE,KAAAU,eAAA,CAAAC,oBAAA,CACnB,KAAM,CAAAC,IAAI,CAAG,EAAAF,eAAA,CAAAT,QAAQ,CAACY,KAAK,UAAAH,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBE,IAAI,UAAAD,oBAAA,iBAApBA,oBAAA,CAAsBG,QAAQ,GAAI,YAAY,CAC3D,mBAAOrB,IAAA,CAACJ,QAAQ,EAAC0B,EAAE,CAAEH,IAAK,CAACI,OAAO,MAAE,CAAC,CACvC,CAEA,KAAM,CAAAC,iBAAiB,CAAIC,CAAsC,EAAK,CACpE,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChClB,WAAW,CAACmB,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP,CAACH,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CACH,GAAId,KAAK,CAAEC,QAAQ,CAAC,EAAE,CAAC,CACzB,CAAC,CAED,KAAM,CAAAgB,YAAY,CAAG,KAAO,CAAAL,CAAmC,EAAK,CAClEA,CAAC,CAACM,cAAc,CAAC,CAAC,CAClBf,YAAY,CAAC,IAAI,CAAC,CAClBF,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA,GAAIL,QAAQ,CAACE,QAAQ,GAAK,OAAO,CAAE,CACjCG,QAAQ,CAAC,yCAAyC,CAAC,CACnDE,YAAY,CAAC,KAAK,CAAC,CACnB,OACF,CAEA,GAAIP,QAAQ,CAACG,QAAQ,GAAK,WAAW,CAAE,CACrCE,QAAQ,CAAC,6CAA6C,CAAC,CACvDE,YAAY,CAAC,KAAK,CAAC,CACnB,OACF,CAEA,GAAI,CACF,KAAM,CAAAgB,MAAM,CAAG1B,KAAK,CAACG,QAAQ,CAAC,CAE9B,GAAI,CAACuB,MAAM,CAACC,OAAO,CAAE,CACnBnB,QAAQ,CAACkB,MAAM,CAACE,OAAO,EAAI,cAAc,CAAC,CAC5C,CACF,CAAE,MAAOC,GAAG,CAAE,CACZrB,QAAQ,CAAC,8BAA8B,CAAC,CAC1C,CAAC,OAAS,CACRE,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,mBACEd,KAAA,QAAKkC,SAAS,CAAC,gEAAgE,CAAAC,QAAA,eAC7ErC,IAAA,QAAKoC,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cACjEnC,KAAA,QAAKkC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCrC,IAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCnC,KAAA,QAAKkC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BrC,IAAA,QACEsC,GAAG,CAAC,sEAAsE,CAC1EC,GAAG,CAAC,WAAW,CACfH,SAAS,CAAC,cAAc,CACxBI,OAAO,CAAGf,CAAC,EAAK,CACd;AACAA,CAAC,CAACgB,aAAa,CAACC,KAAK,CAACC,OAAO,CAAG,MAAM,CACtC,KAAM,CAAAC,QAAQ,CAAGnB,CAAC,CAACgB,aAAa,CAACI,kBAAiC,CAClE,GAAID,QAAQ,CAAEA,QAAQ,CAACF,KAAK,CAACC,OAAO,CAAG,OAAO,CAChD,CAAE,CACH,CAAC,cACF3C,IAAA,QAAKoC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,MAEzD,CAAK,CAAC,EACH,CAAC,CAGH,CAAC,cAENnC,KAAA,QAAKkC,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxEnC,KAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BrC,IAAA,OAAIoC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACvErC,IAAA,MAAGoC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,4CAE7B,CAAG,CAAC,EACD,CAAC,cAENnC,KAAA,SAAM4C,QAAQ,CAAEhB,YAAa,CAACM,SAAS,CAAC,WAAW,CAAAC,QAAA,eACjDnC,KAAA,QAAAmC,QAAA,eACErC,IAAA,UAAO+C,OAAO,CAAC,UAAU,CAACX,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,UAErF,CAAO,CAAC,cACRnC,KAAA,QAAKkC,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrC,IAAA,QAAKoC,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFrC,IAAA,MAAGoC,SAAS,CAAC,2BAA2B,CAAI,CAAC,CAC1C,CAAC,cACNpC,IAAA,UACEgD,EAAE,CAAC,UAAU,CACbtB,IAAI,CAAC,UAAU,CACfuB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,OAAO,CACnBvB,KAAK,CAAElB,QAAQ,CAACE,QAAS,CACzBwC,QAAQ,CAAE3B,iBAAkB,CAC5BY,SAAS,CAAC,sLAAsL,CAChMgB,QAAQ,MACRC,QAAQ,CAAEtC,SAAU,CACrB,CAAC,EACC,CAAC,EACH,CAAC,cAENb,KAAA,QAAAmC,QAAA,eACErC,IAAA,UAAO+C,OAAO,CAAC,UAAU,CAACX,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,UAErF,CAAO,CAAC,cACRnC,KAAA,QAAKkC,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrC,IAAA,QAAKoC,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFrC,IAAA,MAAGoC,SAAS,CAAC,2BAA2B,CAAI,CAAC,CAC1C,CAAC,cACNpC,IAAA,UACEgD,EAAE,CAAC,UAAU,CACbtB,IAAI,CAAC,UAAU,CACfuB,IAAI,CAAC,UAAU,CACfC,WAAW,CAAC,WAAW,CACvBvB,KAAK,CAAElB,QAAQ,CAACG,QAAS,CACzBuC,QAAQ,CAAE3B,iBAAkB,CAC5BY,SAAS,CAAC,sLAAsL,CAChMgB,QAAQ,MACRC,QAAQ,CAAEtC,SAAU,CACrB,CAAC,EACC,CAAC,EACH,CAAC,CAELF,KAAK,eACJX,KAAA,QAAKkC,SAAS,CAAC,iGAAiG,CAAAC,QAAA,eAC9GrC,IAAA,MAAGoC,SAAS,CAAC,2BAA2B,CAAI,CAAC,cAC7CpC,IAAA,SAAAqC,QAAA,CAAOxB,KAAK,CAAO,CAAC,EACjB,CACN,cAEDb,IAAA,WACEiD,IAAI,CAAC,QAAQ,CACbI,QAAQ,CAAEtC,SAAU,CACpBqB,SAAS,CAAC,2NAA2N,CAAAC,QAAA,CAEpOtB,SAAS,cACRb,KAAA,CAAAE,SAAA,EAAAiC,QAAA,eACErC,IAAA,QAAKoC,SAAS,CAAC,mFAAmF,CAAM,CAAC,gBAE3G,EAAE,CAAC,cAEHlC,KAAA,CAAAE,SAAA,EAAAiC,QAAA,eACErC,IAAA,MAAGoC,SAAS,CAAC,yBAAyB,CAAI,CAAC,UAE7C,EAAE,CACH,CACK,CAAC,EACL,CAAC,EACJ,CAAC,EACH,CAAC,CACH,CAAC,cAENlC,KAAA,QAAKkC,SAAS,CAAC,uJAAuJ,CAAAC,QAAA,eACpKrC,IAAA,QAAKoC,SAAS,CAAC,sCAAsC,CAAM,CAAC,cAC5DlC,KAAA,QAAKkC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDnC,KAAA,QAAKkC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrC,IAAA,QAAKoC,SAAS,CAAC,6FAA6F,CAAAC,QAAA,cAC1GrC,IAAA,MAAGoC,SAAS,CAAC,4BAA4B,CAAI,CAAC,CAC3C,CAAC,cACNpC,IAAA,OAAIoC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cAC1DrC,IAAA,MAAGoC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,2HAErD,CAAG,CAAC,EACD,CAAC,cAENnC,KAAA,QAAKkC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CnC,KAAA,QAAKkC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BrC,IAAA,QAAKoC,SAAS,CAAC,2FAA2F,CAAAC,QAAA,cACxGrC,IAAA,MAAGoC,SAAS,CAAC,2BAA2B,CAAI,CAAC,CAC1C,CAAC,cACNpC,IAAA,MAAGoC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,QAAM,CAAG,CAAC,EAC5C,CAAC,cACNnC,KAAA,QAAKkC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BrC,IAAA,QAAKoC,SAAS,CAAC,2FAA2F,CAAAC,QAAA,cACxGrC,IAAA,MAAGoC,SAAS,CAAC,+BAA+B,CAAI,CAAC,CAC9C,CAAC,cACNpC,IAAA,MAAGoC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,MAAI,CAAG,CAAC,EAC1C,CAAC,cACNnC,KAAA,QAAKkC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BrC,IAAA,QAAKoC,SAAS,CAAC,2FAA2F,CAAAC,QAAA,cACxGrC,IAAA,MAAGoC,SAAS,CAAC,sBAAsB,CAAI,CAAC,CACrC,CAAC,cACNpC,IAAA,MAAGoC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,UAAQ,CAAG,CAAC,EAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}