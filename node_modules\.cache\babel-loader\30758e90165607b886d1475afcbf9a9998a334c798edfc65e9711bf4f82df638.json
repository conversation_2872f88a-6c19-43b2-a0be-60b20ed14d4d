{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EmptyState=_ref=>{let{icon='fas fa-inbox',title,description,action,className=''}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:`text-center py-16 ${className}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center space-y-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"i\",{className:`${icon} text-gray-400 text-2xl`})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500 font-medium\",children:title}),description&&/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-sm mt-1\",children:description}),action&&/*#__PURE__*/_jsx(\"button\",{onClick:action.onClick,className:\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\",type:\"button\",children:action.label})]})]})});};export default EmptyState;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "EmptyState", "_ref", "icon", "title", "description", "action", "className", "children", "onClick", "type", "label"], "sources": ["D:/ELGI/src/components/common/EmptyState.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface EmptyStateProps {\n  icon?: string;\n  title: string;\n  description?: string;\n  action?: {\n    label: string;\n    onClick: () => void;\n  };\n  className?: string;\n}\n\nconst EmptyState: React.FC<EmptyStateProps> = ({ \n  icon = 'fas fa-inbox',\n  title, \n  description, \n  action,\n  className = '' \n}) => {\n  return (\n    <div className={`text-center py-16 ${className}`}>\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center\">\n          <i className={`${icon} text-gray-400 text-2xl`} />\n        </div>\n        <div>\n          <p className=\"text-gray-500 font-medium\">{title}</p>\n          {description && (\n            <p className=\"text-gray-400 text-sm mt-1\">{description}</p>\n          )}\n          {action && (\n            <button\n              onClick={action.onClick}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\"\n              type=\"button\"\n            >\n              {action.label}\n            </button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EmptyState;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAa1B,KAAM,CAAAC,UAAqC,CAAGC,IAAA,EAMxC,IANyC,CAC7CC,IAAI,CAAG,cAAc,CACrBC,KAAK,CACLC,WAAW,CACXC,MAAM,CACNC,SAAS,CAAG,EACd,CAAC,CAAAL,IAAA,CACC,mBACEJ,IAAA,QAAKS,SAAS,CAAE,qBAAqBA,SAAS,EAAG,CAAAC,QAAA,cAC/CR,KAAA,QAAKO,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnDV,IAAA,QAAKS,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAClFV,IAAA,MAAGS,SAAS,CAAE,GAAGJ,IAAI,yBAA0B,CAAE,CAAC,CAC/C,CAAC,cACNH,KAAA,QAAAQ,QAAA,eACEV,IAAA,MAAGS,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEJ,KAAK,CAAI,CAAC,CACnDC,WAAW,eACVP,IAAA,MAAGS,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEH,WAAW,CAAI,CAC3D,CACAC,MAAM,eACLR,IAAA,WACEW,OAAO,CAAEH,MAAM,CAACG,OAAQ,CACxBF,SAAS,CAAC,gGAAgG,CAC1GG,IAAI,CAAC,QAAQ,CAAAF,QAAA,CAEZF,MAAM,CAACK,KAAK,CACP,CACT,EACE,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}