{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\common\\\\LoadingSpinner.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'md',\n  message = 'Loading...',\n  className = ''\n}) => {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-12 h-12',\n    lg: 'w-16 h-16'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex flex-col items-center space-y-4 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${sizeClasses[size]} border-4 border-t-transparent rounded-full animate-spin`,\n      style: {\n        borderColor: '#3579F3'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 font-medium\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "message", "className", "sizeClasses", "sm", "md", "lg", "children", "style", "borderColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/common/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  message?: string;\n  className?: string;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'md', \n  message = 'Loading...', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-12 h-12',\n    lg: 'w-16 h-16'\n  };\n\n  return (\n    <div className={`flex flex-col items-center space-y-4 ${className}`}>\n      <div\n        className={`${sizeClasses[size]} border-4 border-t-transparent rounded-full animate-spin`}\n        style={{ borderColor: '#3579F3' }}\n      />\n      {message && <p className=\"text-gray-600 font-medium\">{message}</p>}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ1B,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,IAAI,GAAG,IAAI;EACXC,OAAO,GAAG,YAAY;EACtBC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACN,CAAC;EAED,oBACER,OAAA;IAAKI,SAAS,EAAE,wCAAwCA,SAAS,EAAG;IAAAK,QAAA,gBAClET,OAAA;MACEI,SAAS,EAAE,GAAGC,WAAW,CAACH,IAAI,CAAC,0DAA2D;MAC1FQ,KAAK,EAAE;QAAEC,WAAW,EAAE;MAAU;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,EACDZ,OAAO,iBAAIH,OAAA;MAAGI,SAAS,EAAC,2BAA2B;MAAAK,QAAA,EAAEN;IAAO;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/D,CAAC;AAEV,CAAC;AAACC,EAAA,GApBIf,cAA6C;AAsBnD,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}