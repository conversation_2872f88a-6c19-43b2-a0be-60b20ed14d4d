{"ast": null, "code": "import React from'react';import Header from'./Header';import Sidebar from'./Sidebar';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Layout=_ref=>{let{children}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50 flex\",children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex flex-col\",children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1 p-6\",children:children})]})]});};export default Layout;", "map": {"version": 3, "names": ["React", "Header", "Sidebar", "jsx", "_jsx", "jsxs", "_jsxs", "Layout", "_ref", "children", "className"], "sources": ["D:/ELGI/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      <Sidebar />\n      \n      <div className=\"flex-1 flex flex-col\">\n        <Header />\n        \n        <main className=\"flex-1 p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,UAAU,CAC7B,MAAO,CAAAC,OAAO,KAAM,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMhC,KAAM,CAAAC,MAA6B,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjD,mBACEF,KAAA,QAAKI,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CL,IAAA,CAACF,OAAO,GAAE,CAAC,cAEXI,KAAA,QAAKI,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCL,IAAA,CAACH,MAAM,GAAE,CAAC,cAEVG,IAAA,SAAMM,SAAS,CAAC,YAAY,CAAAD,QAAA,CACzBA,QAAQ,CACL,CAAC,EACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}