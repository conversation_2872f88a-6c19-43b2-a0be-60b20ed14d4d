import React from 'react';
import { NavLink } from 'react-router-dom';

const Sidebar: React.FC = () => {
  const navItems = [
    {
      path: '/dashboard',
      icon: 'fas fa-home',
      label: 'Dashboard'
    },
    {
      path: '/transactions',
      icon: 'fas fa-list',
      label: 'All Transactions'
    }
  ];

  return (
    <div className="w-64 bg-white shadow-sm border-r border-gray-200">
      <div className="p-6">
        {/* Logo */}
        <div className="flex items-center space-x-3 mb-8">
          <img
            src="https://www.elgi.com/sg/wp-content/themes/ELGi/images/elgi__logo.png"
            alt="ELGi Logo"
            className="h-8"
            onError={(e) => {
              // Fallback to text if image fails to load
              e.currentTarget.style.display = 'none';
              const fallback = e.currentTarget.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = 'block';
            }}
          />
          <div className="hidden text-lg font-bold text-gray-900">
            ELGi
          </div>
          <h1 className="text-xl font-bold text-gray-900">Dashboard</h1>
        </div>

        {/* Navigation */}
        <nav className="space-y-2">
          {navItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                  isActive
                    ? 'text-white border'
                    : 'text-gray-600 hover:bg-gray-50'
                }`
              }
              style={({ isActive }) => isActive ? { backgroundColor: '#3579F3', borderColor: '#3579F3' } : {}}
            >
              <i className={`${item.icon} text-sm`}></i>
              <span className="font-medium">{item.label}</span>
            </NavLink>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default Sidebar;
