import axios from "axios";

// -------------------- Configuration --------------------
const BASE_URL = process.env.REACT_APP_BASE_API;

const COMMON_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// Axios instance with base URL
const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: COMMON_HEADERS,
});

// -------------------- Types --------------------
export interface User {
  id: number;
  name: string;
  username: string;
  email: string;
  address: {
    street: string;
    suite: string;
    city: string;
    zipcode: string;
    geo: {
      lat: string;
      lng: string;
    };
  };
  phone: string;
  website: string;
  company: {
    name: string;
    catchPhrase: string;
    bs: string;
  };
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  token?: string;
}

// -------------------- Auth Utilities --------------------
export const AUTH_STORAGE_KEY = 'admin_dashboard_auth';

export const authUtils = {
  isAuthenticated: (): boolean => {
    const authData = localStorage.getItem(AUTH_STORAGE_KEY);
    if (!authData) return false;
    try {
      const parsed = JSON.parse(authData);
      return parsed.isAuthenticated === true;
    } catch {
      return false;
    }
  },

  login: (credentials: LoginCredentials): AuthResponse => {
    // Enforce exact credentials validation
    if (credentials.username === 'admin' && credentials.password === 'admin@123') {
      const authData = {
        isAuthenticated: true,
        username: credentials.username,
        loginTime: new Date().toISOString(),
      };
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));
      return {
        success: true,
        message: 'Login successful',
        token: 'mock-jwt-token'
      };
    }

    // Return specific error messages for better user experience
    if (credentials.username !== 'admin') {
      return {
        success: false,
        message: 'Invalid username. Please use "admin".'
      };
    }

    if (credentials.password !== 'admin@123') {
      return {
        success: false,
        message: 'Invalid password. Please use "admin@123".'
      };
    }

    return {
      success: false,
      message: 'Invalid credentials. Please try again.'
    };
  },

  logout: (): void => {
    localStorage.removeItem(AUTH_STORAGE_KEY);
  },

  getCurrentUser: () => {
    const authData = localStorage.getItem(AUTH_STORAGE_KEY);
    if (!authData) return null;
    try {
      return JSON.parse(authData);
    } catch {
      return null;
    }
  }
};

// -------------------- API Service --------------------
export const apiService = {
  async getDetails() {
    try {
      const response = await apiClient.get('/log/list');
      if (Array.isArray(response.data?.data)) {
        return response.data.data;
      }
      return [];
    } catch (error) {
      throw error;
    }
  },

  async getFilteredDetails(source?: string, status?: string) {
    try {
      const params: any = {};
      if (source) params.source = source;
      if (status) params.status = status;
      const response = await apiClient.get('/log/list', { params });
      if (Array.isArray(response.data?.data)) {
        return response.data.data;
      }
      return [];
    } catch (error) {
      throw error;
    }
  },

  async updateTransaction(id: number, payload: any) {
    try {
      const response = await apiClient.put(`/log/update/${id}`, payload);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  async getUserById(id: number): Promise<User> {
    try {
      const response = await apiClient.get(`/users/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
};

export default apiService;
