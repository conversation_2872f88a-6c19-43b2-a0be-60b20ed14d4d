import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  message = 'Loading...', 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      <div
        className={`${sizeClasses[size]} border-4 border-t-transparent rounded-full animate-spin`}
        style={{ borderColor: '#3579F3' }}
      />
      {message && <p className="text-gray-600 font-medium">{message}</p>}
    </div>
  );
};

export default LoadingSpinner;
