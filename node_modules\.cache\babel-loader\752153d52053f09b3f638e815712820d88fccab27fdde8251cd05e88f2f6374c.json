{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\common\\\\ErrorMessage.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ErrorMessage = ({\n  message,\n  onRetry,\n  className = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `text-center py-16 ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle text-red-600 text-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 font-medium mb-2\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), onRetry && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onRetry,\n          className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-redo mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 15\n          }, this), \"Try Again\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = ErrorMessage;\nexport default ErrorMessage;\nvar _c;\n$RefreshReg$(_c, \"ErrorMessage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ErrorMessage", "message", "onRetry", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/common/ErrorMessage.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onRetry?: () => void;\n  className?: string;\n}\n\nconst ErrorMessage: React.FC<ErrorMessageProps> = ({ \n  message, \n  onRetry, \n  className = '' \n}) => {\n  return (\n    <div className={`text-center py-16 ${className}`}>\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center\">\n          <i className=\"fas fa-exclamation-triangle text-red-600 text-2xl\" />\n        </div>\n        <div>\n          <p className=\"text-red-600 font-medium mb-2\">{message}</p>\n          {onRetry && (\n            <button\n              onClick={onRetry}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\"\n              type=\"button\"\n            >\n              <i className=\"fas fa-redo mr-2\"></i>\n              Try Again\n            </button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ErrorMessage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ1B,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,OAAO;EACPC,OAAO;EACPC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,oBACEJ,OAAA;IAAKI,SAAS,EAAE,qBAAqBA,SAAS,EAAG;IAAAC,QAAA,eAC/CL,OAAA;MAAKI,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDL,OAAA;QAAKI,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjFL,OAAA;UAAGI,SAAS,EAAC;QAAmD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACNT,OAAA;QAAAK,QAAA,gBACEL,OAAA;UAAGI,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAEH;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACzDN,OAAO,iBACNH,OAAA;UACEU,OAAO,EAAEP,OAAQ;UACjBC,SAAS,EAAC,2FAA2F;UACrGO,IAAI,EAAC,QAAQ;UAAAN,QAAA,gBAEbL,OAAA;YAAGI,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,aAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GA3BIX,YAAyC;AA6B/C,eAAeA,YAAY;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}