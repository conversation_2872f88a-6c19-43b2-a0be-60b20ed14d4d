{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\";\nimport React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = () => {\n  const navItems = [{\n    path: '/dashboard',\n    icon: 'fas fa-home',\n    label: 'Dashboard'\n  }, {\n    path: '/transactions',\n    icon: 'fas fa-list',\n    label: 'All Transactions'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-64 bg-white shadow-sm border-r border-gray-200\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://www.elgi.com/in-compressors/wp-content/themes/ELGi/Issame-EPSAC-Page/template-common-images/logo.png\",\n          alt: \"ELGi Logo\",\n          className: \"h-8\",\n          onError: e => {\n            // Fallback to icon if image fails to load\n            e.currentTarget.style.display = 'none';\n            const fallback = e.currentTarget.nextElementSibling;\n            if (fallback) fallback.style.display = 'flex';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 rounded-lg items-center justify-center hidden\",\n          style: {\n            backgroundColor: '#3579F3'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-cube text-white text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-bold text-gray-900\",\n          children: \"ELGi Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"space-y-2\",\n        children: navItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: ({\n            isActive\n          }) => `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${isActive ? 'text-white border' : 'text-gray-600 hover:bg-gray-50'}`,\n          style: ({\n            isActive\n          }) => isActive ? {\n            backgroundColor: '#3579F3',\n            borderColor: '#3579F3'\n          } : {},\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: `${item.icon} text-sm`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "jsxDEV", "_jsxDEV", "Sidebar", "navItems", "path", "icon", "label", "className", "children", "src", "alt", "onError", "e", "currentTarget", "style", "display", "fallback", "nextElement<PERSON><PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "backgroundColor", "map", "item", "to", "isActive", "borderColor", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink } from 'react-router-dom';\n\nconst Sidebar: React.FC = () => {\n  const navItems = [\n    {\n      path: '/dashboard',\n      icon: 'fas fa-home',\n      label: 'Dashboard'\n    },\n    {\n      path: '/transactions',\n      icon: 'fas fa-list',\n      label: 'All Transactions'\n    }\n  ];\n\n  return (\n    <div className=\"w-64 bg-white shadow-sm border-r border-gray-200\">\n      <div className=\"p-6\">\n        {/* Logo */}\n        <div className=\"flex items-center space-x-3 mb-8\">\n          <img\n            src=\"https://www.elgi.com/in-compressors/wp-content/themes/ELGi/Issame-EPSAC-Page/template-common-images/logo.png\"\n            alt=\"ELGi Logo\"\n            className=\"h-8\"\n            onError={(e) => {\n              // Fallback to icon if image fails to load\n              e.currentTarget.style.display = 'none';\n              const fallback = e.currentTarget.nextElementSibling as HTMLElement;\n              if (fallback) fallback.style.display = 'flex';\n            }}\n          />\n          <div className=\"w-8 h-8 rounded-lg items-center justify-center hidden\" style={{ backgroundColor: '#3579F3' }}>\n            <i className=\"fas fa-cube text-white text-sm\"></i>\n          </div>\n          <h1 className=\"text-xl font-bold text-gray-900\">ELGi Dashboard</h1>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"space-y-2\">\n          {navItems.map((item) => (\n            <NavLink\n              key={item.path}\n              to={item.path}\n              className={({ isActive }) =>\n                `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${\n                  isActive\n                    ? 'text-white border'\n                    : 'text-gray-600 hover:bg-gray-50'\n                }`\n              }\n              style={({ isActive }) => isActive ? { backgroundColor: '#3579F3', borderColor: '#3579F3' } : {}}\n            >\n              <i className={`${item.icon} text-sm`}></i>\n              <span className=\"font-medium\">{item.label}</span>\n            </NavLink>\n          ))}\n        </nav>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEL,OAAA;IAAKM,SAAS,EAAC,kDAAkD;IAAAC,QAAA,eAC/DP,OAAA;MAAKM,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAElBP,OAAA;QAAKM,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CP,OAAA;UACEQ,GAAG,EAAC,8GAA8G;UAClHC,GAAG,EAAC,WAAW;UACfH,SAAS,EAAC,KAAK;UACfI,OAAO,EAAGC,CAAC,IAAK;YACd;YACAA,CAAC,CAACC,aAAa,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;YACtC,MAAMC,QAAQ,GAAGJ,CAAC,CAACC,aAAa,CAACI,kBAAiC;YAClE,IAAID,QAAQ,EAAEA,QAAQ,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;UAC/C;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFpB,OAAA;UAAKM,SAAS,EAAC,uDAAuD;UAACO,KAAK,EAAE;YAAEQ,eAAe,EAAE;UAAU,CAAE;UAAAd,QAAA,eAC3GP,OAAA;YAAGM,SAAS,EAAC;UAAgC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACNpB,OAAA;UAAIM,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAAc;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eAGNpB,OAAA;QAAKM,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBL,QAAQ,CAACoB,GAAG,CAAEC,IAAI,iBACjBvB,OAAA,CAACF,OAAO;UAEN0B,EAAE,EAAED,IAAI,CAACpB,IAAK;UACdG,SAAS,EAAEA,CAAC;YAAEmB;UAAS,CAAC,KACtB,uFACEA,QAAQ,GACJ,mBAAmB,GACnB,gCAAgC,EAEvC;UACDZ,KAAK,EAAEA,CAAC;YAAEY;UAAS,CAAC,KAAKA,QAAQ,GAAG;YAAEJ,eAAe,EAAE,SAAS;YAAEK,WAAW,EAAE;UAAU,CAAC,GAAG,CAAC,CAAE;UAAAnB,QAAA,gBAEhGP,OAAA;YAAGM,SAAS,EAAE,GAAGiB,IAAI,CAACnB,IAAI;UAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1CpB,OAAA;YAAMM,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEgB,IAAI,CAAClB;UAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAZ5CG,IAAI,CAACpB,IAAI;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaP,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACO,EAAA,GA3DI1B,OAAiB;AA6DvB,eAAeA,OAAO;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}