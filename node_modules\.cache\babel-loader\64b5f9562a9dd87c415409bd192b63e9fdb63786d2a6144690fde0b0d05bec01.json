{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\";\nimport React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = () => {\n  const navItems = [{\n    path: '/dashboard',\n    icon: 'fas fa-home',\n    label: 'Dashboard'\n  }, {\n    path: '/transactions',\n    icon: 'fas fa-list',\n    label: 'All Transactions'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-64 bg-white shadow-sm border-r border-gray-200\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(ELGiLogo, {\n          width: 96,\n          height: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-bold text-gray-900\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"space-y-2\",\n        children: navItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: ({\n            isActive\n          }) => `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${isActive ? 'text-white border' : 'text-gray-600 hover:bg-gray-50'}`,\n          style: ({\n            isActive\n          }) => isActive ? {\n            backgroundColor: '#3579F3',\n            borderColor: '#3579F3'\n          } : {},\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: `${item.icon} text-sm`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "jsxDEV", "_jsxDEV", "Sidebar", "navItems", "path", "icon", "label", "className", "children", "ELGiLogo", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "to", "isActive", "style", "backgroundColor", "borderColor", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink } from 'react-router-dom';\n\nconst Sidebar: React.FC = () => {\n  const navItems = [\n    {\n      path: '/dashboard',\n      icon: 'fas fa-home',\n      label: 'Dashboard'\n    },\n    {\n      path: '/transactions',\n      icon: 'fas fa-list',\n      label: 'All Transactions'\n    }\n  ];\n\n  return (\n    <div className=\"w-64 bg-white shadow-sm border-r border-gray-200\">\n      <div className=\"p-6\">\n        {/* Logo */}\n        <div className=\"flex items-center space-x-3 mb-8\">\n          <ELGiLogo width={96} height={32} />\n          <h1 className=\"text-xl font-bold text-gray-900\">Dashboard</h1>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"space-y-2\">\n          {navItems.map((item) => (\n            <NavLink\n              key={item.path}\n              to={item.path}\n              className={({ isActive }) =>\n                `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${\n                  isActive\n                    ? 'text-white border'\n                    : 'text-gray-600 hover:bg-gray-50'\n                }`\n              }\n              style={({ isActive }) => isActive ? { backgroundColor: '#3579F3', borderColor: '#3579F3' } : {}}\n            >\n              <i className={`${item.icon} text-sm`}></i>\n              <span className=\"font-medium\">{item.label}</span>\n            </NavLink>\n          ))}\n        </nav>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEL,OAAA;IAAKM,SAAS,EAAC,kDAAkD;IAAAC,QAAA,eAC/DP,OAAA;MAAKM,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAElBP,OAAA;QAAKM,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CP,OAAA,CAACQ,QAAQ;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnCd,OAAA;UAAIM,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAGNd,OAAA;QAAKM,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBL,QAAQ,CAACa,GAAG,CAAEC,IAAI,iBACjBhB,OAAA,CAACF,OAAO;UAENmB,EAAE,EAAED,IAAI,CAACb,IAAK;UACdG,SAAS,EAAEA,CAAC;YAAEY;UAAS,CAAC,KACtB,uFACEA,QAAQ,GACJ,mBAAmB,GACnB,gCAAgC,EAEvC;UACDC,KAAK,EAAEA,CAAC;YAAED;UAAS,CAAC,KAAKA,QAAQ,GAAG;YAAEE,eAAe,EAAE,SAAS;YAAEC,WAAW,EAAE;UAAU,CAAC,GAAG,CAAC,CAAE;UAAAd,QAAA,gBAEhGP,OAAA;YAAGM,SAAS,EAAE,GAAGU,IAAI,CAACZ,IAAI;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1Cd,OAAA;YAAMM,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAES,IAAI,CAACX;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAZ5CE,IAAI,CAACb,IAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaP,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACQ,EAAA,GA9CIrB,OAAiB;AAgDvB,eAAeA,OAAO;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}