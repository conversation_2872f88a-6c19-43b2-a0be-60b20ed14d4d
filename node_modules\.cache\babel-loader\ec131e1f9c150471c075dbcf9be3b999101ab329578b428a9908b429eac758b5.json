{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\AllTransactions\\\\AllTransactions.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport Layout from '../Layout/Layout';\nimport { apiService } from '../../api';\nimport './AllTransactions.css'; // Import CSS for tooltip\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllTransactions = () => {\n  _s();\n  const [transactions, setTransactions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        var _response$data;\n        const response = await apiService.get();\n        const data = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.data) || [];\n        const mappedData = data.map(item => {\n          const detailsObj = JSON.parse(item.details || '{}');\n          return {\n            id: item.id,\n            email: item.email || '',\n            name: detailsObj.receiver_name || '',\n            product_source: detailsObj.product_source || '',\n            details: item.details || '',\n            reason: detailsObj.reason || '',\n            status: detailsObj.status || 'Pending'\n          };\n        });\n        setTransactions(mappedData);\n      } catch (err) {\n        setError('Failed to fetch transactions');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(Layout, {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(Layout, {\n    children: [\"Error: \", error]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"All Transactions\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"transaction-table\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Source\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Reason\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: transactions.map(txn => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: txn.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: txn.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: txn.product_source\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tooltip\",\n              children: [\"View\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"tooltiptext\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  children: txn.details\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: txn.reason\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: txn.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              children: \"Edit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                marginLeft: '8px'\n              },\n              children: \"Delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this)]\n        }, txn.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(AllTransactions, \"8aGrckzwS5iaQE2eUfgw2C1DaYg=\");\n_c = AllTransactions;\nexport default AllTransactions;\nvar _c;\n$RefreshReg$(_c, \"AllTransactions\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Layout", "apiService", "jsxDEV", "_jsxDEV", "AllTransactions", "_s", "transactions", "setTransactions", "loading", "setLoading", "error", "setError", "fetchData", "_response$data", "response", "get", "data", "mappedData", "map", "item", "detailsObj", "JSON", "parse", "details", "id", "email", "name", "receiver_name", "product_source", "reason", "status", "err", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "txn", "style", "marginLeft", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/AllTransactions/AllTransactions.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Layout from '../Layout/Layout';\nimport { apiService } from '../../api';\nimport './AllTransactions.css'; // Import CSS for tooltip\n\ninterface Transaction {\n  id: number;\n  email: string;\n  name: string;\n  product_source: string;\n  details: string;\n  reason: string;\n  status: string;\n}\n\nconst AllTransactions: React.FC = () => {\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const response = await apiService.get();\n        const data = response.data?.data || [];\n\n        const mappedData = data.map((item: any) => {\n          const detailsObj = JSON.parse(item.details || '{}');\n\n          return {\n            id: item.id,\n            email: item.email || '',\n            name: detailsObj.receiver_name || '',\n            product_source: detailsObj.product_source || '',\n            details: item.details || '',\n            reason: detailsObj.reason || '',\n            status: detailsObj.status || 'Pending',\n          };\n        });\n\n        setTransactions(mappedData);\n      } catch (err: any) {\n        setError('Failed to fetch transactions');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  if (loading) return <Layout>Loading...</Layout>;\n  if (error) return <Layout>Error: {error}</Layout>;\n\n  return (\n    <Layout>\n      <h2>All Transactions</h2>\n      <table className=\"transaction-table\">\n        <thead>\n          <tr>\n            <th>Name</th>\n            <th>Email</th>\n            <th>Source</th>\n            <th>Details</th>\n            <th>Reason</th>\n            <th>Status</th>\n            <th>Action</th>\n          </tr>\n        </thead>\n        <tbody>\n          {transactions.map((txn) => (\n            <tr key={txn.id}>\n              <td>{txn.name}</td>\n              <td>{txn.email}</td>\n              <td>{txn.product_source}</td>\n              <td>\n                <span className=\"tooltip\">\n                  View\n                  <span className=\"tooltiptext\">\n                    <pre>{txn.details}</pre>\n                  </span>\n                </span>\n              </td>\n              <td>{txn.reason}</td>\n              <td>{txn.status}</td>\n              <td>\n                <button>Edit</button>\n                <button style={{ marginLeft: '8px' }}>Delete</button>\n              </td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </Layout>\n  );\n};\n\nexport default AllTransactions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,UAAU,QAAQ,WAAW;AACtC,OAAO,uBAAuB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAYhC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGR,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAS,EAAE,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACd,MAAMc,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QAAA,IAAAC,cAAA;QACF,MAAMC,QAAQ,GAAG,MAAMb,UAAU,CAACc,GAAG,CAAC,CAAC;QACvC,MAAMC,IAAI,GAAG,EAAAH,cAAA,GAAAC,QAAQ,CAACE,IAAI,cAAAH,cAAA,uBAAbA,cAAA,CAAeG,IAAI,KAAI,EAAE;QAEtC,MAAMC,UAAU,GAAGD,IAAI,CAACE,GAAG,CAAEC,IAAS,IAAK;UACzC,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,OAAO,IAAI,IAAI,CAAC;UAEnD,OAAO;YACLC,EAAE,EAAEL,IAAI,CAACK,EAAE;YACXC,KAAK,EAAEN,IAAI,CAACM,KAAK,IAAI,EAAE;YACvBC,IAAI,EAAEN,UAAU,CAACO,aAAa,IAAI,EAAE;YACpCC,cAAc,EAAER,UAAU,CAACQ,cAAc,IAAI,EAAE;YAC/CL,OAAO,EAAEJ,IAAI,CAACI,OAAO,IAAI,EAAE;YAC3BM,MAAM,EAAET,UAAU,CAACS,MAAM,IAAI,EAAE;YAC/BC,MAAM,EAAEV,UAAU,CAACU,MAAM,IAAI;UAC/B,CAAC;QACH,CAAC,CAAC;QAEFvB,eAAe,CAACU,UAAU,CAAC;MAC7B,CAAC,CAAC,OAAOc,GAAQ,EAAE;QACjBpB,QAAQ,CAAC,8BAA8B,CAAC;MAC1C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIJ,OAAO,EAAE,oBAAOL,OAAA,CAACH,MAAM;IAAAgC,QAAA,EAAC;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;EAC/C,IAAI1B,KAAK,EAAE,oBAAOP,OAAA,CAACH,MAAM;IAAAgC,QAAA,GAAC,SAAO,EAACtB,KAAK;EAAA;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAS,CAAC;EAEjD,oBACEjC,OAAA,CAACH,MAAM;IAAAgC,QAAA,gBACL7B,OAAA;MAAA6B,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzBjC,OAAA;MAAOkC,SAAS,EAAC,mBAAmB;MAAAL,QAAA,gBAClC7B,OAAA;QAAA6B,QAAA,eACE7B,OAAA;UAAA6B,QAAA,gBACE7B,OAAA;YAAA6B,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACbjC,OAAA;YAAA6B,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdjC,OAAA;YAAA6B,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfjC,OAAA;YAAA6B,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBjC,OAAA;YAAA6B,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfjC,OAAA;YAAA6B,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfjC,OAAA;YAAA6B,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRjC,OAAA;QAAA6B,QAAA,EACG1B,YAAY,CAACY,GAAG,CAAEoB,GAAG,iBACpBnC,OAAA;UAAA6B,QAAA,gBACE7B,OAAA;YAAA6B,QAAA,EAAKM,GAAG,CAACZ;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnBjC,OAAA;YAAA6B,QAAA,EAAKM,GAAG,CAACb;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpBjC,OAAA;YAAA6B,QAAA,EAAKM,GAAG,CAACV;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7BjC,OAAA;YAAA6B,QAAA,eACE7B,OAAA;cAAMkC,SAAS,EAAC,SAAS;cAAAL,QAAA,GAAC,MAExB,eAAA7B,OAAA;gBAAMkC,SAAS,EAAC,aAAa;gBAAAL,QAAA,eAC3B7B,OAAA;kBAAA6B,QAAA,EAAMM,GAAG,CAACf;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLjC,OAAA;YAAA6B,QAAA,EAAKM,GAAG,CAACT;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBjC,OAAA;YAAA6B,QAAA,EAAKM,GAAG,CAACR;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBjC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAA6B,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrBjC,OAAA;cAAQoC,KAAK,EAAE;gBAAEC,UAAU,EAAE;cAAM,CAAE;cAAAR,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA,GAjBEE,GAAG,CAACd,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBX,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEb,CAAC;AAAC/B,EAAA,CAhFID,eAAyB;AAAAqC,EAAA,GAAzBrC,eAAyB;AAkF/B,eAAeA,eAAe;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}