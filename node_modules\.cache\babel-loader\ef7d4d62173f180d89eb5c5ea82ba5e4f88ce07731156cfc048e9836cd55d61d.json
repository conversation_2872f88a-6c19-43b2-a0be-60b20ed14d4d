{"ast": null, "code": "// API service layer for the admin dashboard\nconst BASE_URL = process.env.REACT_APP_BASE_API || 'https://8sg83j33-3000.inc1.devtunnels.ms';\n\n// Types\n\n// Authentication utilities\nexport const AUTH_STORAGE_KEY = 'admin_dashboard_auth';\nexport const authUtils = {\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return false;\n    try {\n      const parsed = JSON.parse(authData);\n      return parsed.isAuthenticated === true;\n    } catch {\n      return false;\n    }\n  },\n  // Login user\n  login: credentials => {\n    // Hardcoded credentials as per requirements\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      const authData = {\n        isAuthenticated: true,\n        username: credentials.username,\n        loginTime: new Date().toISOString()\n      };\n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));\n      return {\n        success: true,\n        message: 'Login successful',\n        token: 'mock-jwt-token'\n      };\n    }\n    return {\n      success: false,\n      message: 'Invalid credentials. Please try again.'\n    };\n  },\n  // Logout user\n  logout: () => {\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n  },\n  // Get current user info\n  getCurrentUser: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return null;\n    try {\n      return JSON.parse(authData);\n    } catch {\n      return null;\n    }\n  }\n};\n\n// API service with your preferred structure\nexport const apiService = {\n  async getUsers() {\n    try {\n      const response = await fetch(`${BASE_URL}/log/list`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch users');\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      throw error;\n    }\n  },\n  async getUserById(id) {\n    try {\n      const response = await fetch(`${BASE_URL}/users/${id}`);\n      if (!response.ok) {\n        throw new Error(`Failed to fetch user with id ${id}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error(`Error fetching user ${id}:`, error);\n      throw error;\n    }\n  },\n  // Mock dashboard stats (since JSONPlaceholder doesn't have this data)\n  async getDashboardStats() {\n    try {\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      return {\n        facebook: {\n          count: 23,\n          change: 12,\n          trend: 'up'\n        },\n        google: {\n          count: 55,\n          change: 8,\n          trend: 'up'\n        },\n        twitter: {\n          count: 34,\n          change: -3,\n          trend: 'down'\n        },\n        instagram: {\n          count: 42,\n          change: 15,\n          trend: 'up'\n        }\n      };\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      throw error;\n    }\n  },\n  // Mock recent activity data\n  async getRecentActivity() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 300));\n      return [{\n        id: 1,\n        message: 'New user registered',\n        time: '2 mins ago',\n        type: 'success'\n      }, {\n        id: 2,\n        message: 'Transaction completed',\n        time: '5 mins ago',\n        type: 'info'\n      }, {\n        id: 3,\n        message: 'System update available',\n        time: '1 hour ago',\n        type: 'warning'\n      }];\n    } catch (error) {\n      console.error('Error fetching recent activity:', error);\n      throw error;\n    }\n  },\n  // Mock quick stats\n  async getQuickStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 200));\n      return {\n        totalUsers: 1234,\n        activeSessions: 89,\n        revenue: '$12,345'\n      };\n    } catch (error) {\n      console.error('Error fetching quick stats:', error);\n      throw error;\n    }\n  }\n};\n\n// Keep the old api export for backward compatibility\nexport const api = apiService;\nexport default apiService;", "map": {"version": 3, "names": ["BASE_URL", "process", "env", "REACT_APP_BASE_API", "AUTH_STORAGE_KEY", "authUtils", "isAuthenticated", "authData", "localStorage", "getItem", "parsed", "JSON", "parse", "login", "credentials", "username", "password", "loginTime", "Date", "toISOString", "setItem", "stringify", "success", "message", "token", "logout", "removeItem", "getCurrentUser", "apiService", "getUsers", "response", "fetch", "ok", "Error", "json", "error", "console", "getUserById", "id", "getDashboardStats", "Promise", "resolve", "setTimeout", "facebook", "count", "change", "trend", "google", "twitter", "instagram", "getRecentActivity", "time", "type", "getQuickStats", "totalUsers", "activeSessions", "revenue", "api"], "sources": ["D:/ELGI/src/api.ts"], "sourcesContent": ["// API service layer for the admin dashboard\nconst BASE_URL = process.env.REACT_APP_BASE_API || 'https://8sg83j33-3000.inc1.devtunnels.ms';\n\n// Types\nexport interface User {\n  id: number;\n  name: string;\n  username: string;\n  email: string;\n  address: {\n    street: string;\n    suite: string;\n    city: string;\n    zipcode: string;\n    geo: {\n      lat: string;\n      lng: string;\n    };\n  };\n  phone: string;\n  website: string;\n  company: {\n    name: string;\n    catchPhrase: string;\n    bs: string;\n  };\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message?: string;\n  token?: string;\n}\n\n// Authentication utilities\nexport const AUTH_STORAGE_KEY = 'admin_dashboard_auth';\n\nexport const authUtils = {\n  // Check if user is authenticated\n  isAuthenticated: (): boolean => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return false;\n    \n    try {\n      const parsed = JSON.parse(authData);\n      return parsed.isAuthenticated === true;\n    } catch {\n      return false;\n    }\n  },\n\n  // Login user\n  login: (credentials: LoginCredentials): AuthResponse => {\n    // Hardcoded credentials as per requirements\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      const authData = {\n        isAuthenticated: true,\n        username: credentials.username,\n        loginTime: new Date().toISOString(),\n      };\n      \n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));\n      \n      return {\n        success: true,\n        message: 'Login successful',\n        token: 'mock-jwt-token'\n      };\n    }\n    \n    return {\n      success: false,\n      message: 'Invalid credentials. Please try again.'\n    };\n  },\n\n  // Logout user\n  logout: (): void => {\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n  },\n\n  // Get current user info\n  getCurrentUser: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return null;\n    \n    try {\n      return JSON.parse(authData);\n    } catch {\n      return null;\n    }\n  }\n};\n\n// API service with your preferred structure\nexport const apiService = {\n  async getUsers() {\n    try {\n      const response = await fetch(`${BASE_URL}/log/list`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch users');\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      throw error;\n    }\n  },\n\n  async getUserById(id: number): Promise<User> {\n    try {\n      const response = await fetch(`${BASE_URL}/users/${id}`);\n      if (!response.ok) {\n        throw new Error(`Failed to fetch user with id ${id}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error(`Error fetching user ${id}:`, error);\n      throw error;\n    }\n  },\n\n  // Mock dashboard stats (since JSONPlaceholder doesn't have this data)\n  async getDashboardStats() {\n    try {\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      return {\n        facebook: { count: 23, change: 12, trend: 'up' as const },\n        google: { count: 55, change: 8, trend: 'up' as const },\n        twitter: { count: 34, change: -3, trend: 'down' as const },\n        instagram: { count: 42, change: 15, trend: 'up' as const },\n      };\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      throw error;\n    }\n  },\n\n  // Mock recent activity data\n  async getRecentActivity() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 300));\n\n      return [\n        { id: 1, message: 'New user registered', time: '2 mins ago', type: 'success' as const },\n        { id: 2, message: 'Transaction completed', time: '5 mins ago', type: 'info' as const },\n        { id: 3, message: 'System update available', time: '1 hour ago', type: 'warning' as const },\n      ];\n    } catch (error) {\n      console.error('Error fetching recent activity:', error);\n      throw error;\n    }\n  },\n\n  // Mock quick stats\n  async getQuickStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      return {\n        totalUsers: 1234,\n        activeSessions: 89,\n        revenue: '$12,345',\n      };\n    } catch (error) {\n      console.error('Error fetching quick stats:', error);\n      throw error;\n    }\n  }\n};\n\n// Keep the old api export for backward compatibility\nexport const api = apiService;\n\nexport default apiService;\n"], "mappings": "AAAA;AACA,MAAMA,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,IAAI,0CAA0C;;AAE7F;;AAoCA;AACA,OAAO,MAAMC,gBAAgB,GAAG,sBAAsB;AAEtD,OAAO,MAAMC,SAAS,GAAG;EACvB;EACAC,eAAe,EAAEA,CAAA,KAAe;IAC9B,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC;IACvD,IAAI,CAACG,QAAQ,EAAE,OAAO,KAAK;IAE3B,IAAI;MACF,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;MACnC,OAAOG,MAAM,CAACJ,eAAe,KAAK,IAAI;IACxC,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF,CAAC;EAED;EACAO,KAAK,EAAGC,WAA6B,IAAmB;IACtD;IACA,IAAIA,WAAW,CAACC,QAAQ,KAAK,OAAO,IAAID,WAAW,CAACE,QAAQ,KAAK,WAAW,EAAE;MAC5E,MAAMT,QAAQ,GAAG;QACfD,eAAe,EAAE,IAAI;QACrBS,QAAQ,EAAED,WAAW,CAACC,QAAQ;QAC9BE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAEDX,YAAY,CAACY,OAAO,CAAChB,gBAAgB,EAAEO,IAAI,CAACU,SAAS,CAACd,QAAQ,CAAC,CAAC;MAEhE,OAAO;QACLe,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,kBAAkB;QAC3BC,KAAK,EAAE;MACT,CAAC;IACH;IAEA,OAAO;MACLF,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED;EACAE,MAAM,EAAEA,CAAA,KAAY;IAClBjB,YAAY,CAACkB,UAAU,CAACtB,gBAAgB,CAAC;EAC3C,CAAC;EAED;EACAuB,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAMpB,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC;IACvD,IAAI,CAACG,QAAQ,EAAE,OAAO,IAAI;IAE1B,IAAI;MACF,OAAOI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;IAC7B,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMqB,UAAU,GAAG;EACxB,MAAMC,QAAQA,CAAA,EAAG;IACf,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/B,QAAQ,WAAW,CAAC;MACpD,IAAI,CAAC8B,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MACA,OAAO,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAME,WAAWA,CAACC,EAAU,EAAiB;IAC3C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/B,QAAQ,UAAUsC,EAAE,EAAE,CAAC;MACvD,IAAI,CAACR,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gCAAgCK,EAAE,EAAE,CAAC;MACvD;MACA,OAAO,MAAMR,QAAQ,CAACI,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuBG,EAAE,GAAG,EAAEH,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMI,iBAAiBA,CAAA,EAAG;IACxB,IAAI;MACF;MACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD,OAAO;QACLE,QAAQ,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAc,CAAC;QACzDC,MAAM,EAAE;UAAEH,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAc,CAAC;QACtDE,OAAO,EAAE;UAAEJ,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,CAAC,CAAC;UAAEC,KAAK,EAAE;QAAgB,CAAC;QAC1DG,SAAS,EAAE;UAAEL,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAc;MAC3D,CAAC;IACH,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMe,iBAAiBA,CAAA,EAAG;IACxB,IAAI;MACF,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD,OAAO,CACL;QAAEH,EAAE,EAAE,CAAC;QAAEf,OAAO,EAAE,qBAAqB;QAAE4B,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAmB,CAAC,EACvF;QAAEd,EAAE,EAAE,CAAC;QAAEf,OAAO,EAAE,uBAAuB;QAAE4B,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAgB,CAAC,EACtF;QAAEd,EAAE,EAAE,CAAC;QAAEf,OAAO,EAAE,yBAAyB;QAAE4B,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAmB,CAAC,CAC5F;IACH,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMkB,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAM,IAAIb,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD,OAAO;QACLa,UAAU,EAAE,IAAI;QAChBC,cAAc,EAAE,EAAE;QAClBC,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMsB,GAAG,GAAG7B,UAAU;AAE7B,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}