{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const location = useLocation();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(ProtectedRoute, \"fNj96oVmPd4sFazcimgf9N7S8ao=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "_s", "isAuthenticated", "loading", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/ProtectedRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport LoadingSpinner from './common/LoadingSpinner';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {\n  const { isAuthenticated, loading } = useAuth();\n  const location = useLocation();\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOlD,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC9C,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,IAAIU,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKS,SAAS,EAAC,+CAA+C;MAAAL,QAAA,eAC5DJ,OAAA;QAAKS,SAAS,EAAC,aAAa;QAAAL,QAAA,gBAC1BJ,OAAA;UAAKS,SAAS,EAAC;QAAgG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtHb,OAAA;UAAGS,SAAS,EAAC,eAAe;UAAAL,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACP,eAAe,EAAE;IACpB;IACA,oBAAON,OAAA,CAACJ,QAAQ;MAACkB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAER;MAAS,CAAE;MAACS,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;EAEA,oBAAOb,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CArBIF,cAA6C;EAAA,QACZL,OAAO,EAC3BD,WAAW;AAAA;AAAAqB,EAAA,GAFxBf,cAA6C;AAuBnD,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}