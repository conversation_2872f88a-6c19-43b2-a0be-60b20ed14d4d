{"ast": null, "code": "import React from'react';import{<PERSON>rowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{AuthProvider}from'./contexts/AuthContext';import ProtectedRoute from'./components/ProtectedRoute';import Login from'./components/Login/Login';import Dashboard from'./components/Dashboard/Dashboard';import AllTransactions from'./components/AllTransactions/AllTransactions';import'./index.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const App=()=>{return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Dashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/transactions\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AllTransactions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})})]})})})});};export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "<PERSON><PERSON>", "Dashboard", "AllTransactions", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "path", "element", "to", "replace"], "sources": ["D:/ELGI/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Login from './components/Login/Login';\nimport Dashboard from './components/Dashboard/Dashboard';\nimport AllTransactions from './components/AllTransactions/AllTransactions';\nimport './index.css';\n\nconst App: React.FC = () => {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            {/* Public Routes */}\n            <Route path=\"/login\" element={<Login />} />\n            \n            {/* Protected Routes */}\n            <Route \n              path=\"/dashboard\" \n              element={\n                <ProtectedRoute>\n                  <Dashboard />\n                </ProtectedRoute>\n              } \n            />\n            \n            <Route \n              path=\"/transactions\" \n              element={\n                <ProtectedRoute>\n                  <AllTransactions />\n                </ProtectedRoute>\n              } \n            />\n            \n            {/* Default redirect */}\n            <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\n            \n            {/* Catch all - redirect to dashboard */}\n            <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n          </Routes>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n};\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,YAAY,KAAQ,wBAAwB,CACrD,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,KAAK,KAAM,0BAA0B,CAC5C,MAAO,CAAAC,SAAS,KAAM,kCAAkC,CACxD,MAAO,CAAAC,eAAe,KAAM,8CAA8C,CAC1E,MAAO,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErB,KAAM,CAAAC,GAAa,CAAGA,CAAA,GAAM,CAC1B,mBACEH,IAAA,CAACN,YAAY,EAAAU,QAAA,cACXJ,IAAA,CAACV,MAAM,EAAAc,QAAA,cACLJ,IAAA,QAAKK,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBF,KAAA,CAACX,MAAM,EAAAa,QAAA,eAELJ,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEP,IAAA,CAACJ,KAAK,GAAE,CAAE,CAAE,CAAC,cAG3CI,IAAA,CAACR,KAAK,EACJc,IAAI,CAAC,YAAY,CACjBC,OAAO,cACLP,IAAA,CAACL,cAAc,EAAAS,QAAA,cACbJ,IAAA,CAACH,SAAS,GAAE,CAAC,CACC,CACjB,CACF,CAAC,cAEFG,IAAA,CAACR,KAAK,EACJc,IAAI,CAAC,eAAe,CACpBC,OAAO,cACLP,IAAA,CAACL,cAAc,EAAAS,QAAA,cACbJ,IAAA,CAACF,eAAe,GAAE,CAAC,CACL,CACjB,CACF,CAAC,cAGFE,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACP,QAAQ,EAACe,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAGjET,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACP,QAAQ,EAACe,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAC3D,CAAC,CACN,CAAC,CACA,CAAC,CACG,CAAC,CAEnB,CAAC,CAED,cAAe,CAAAN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}