import React, { useState, useEffect } from 'react';
import Layout from '../Layout/Layout';
import { apiService } from '../../api';
import { Pagination, LoadingSpinner, ErrorMessage, EmptyState } from '../common';

interface Transaction {
  id: number;
  email: string;
  details: any;
  payload: any;
  status: string;
  reason: string | null;
  source: string;
  created_at: string;
}

interface EditModalProps {
  transaction: Transaction | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedPayload: any) => void;
}

const KeyValueList: React.FC<{ data: any; maxItems?: number }> = ({ data, maxItems = 10 }) => {
  if (!data || typeof data !== 'object') return <span>N/A</span>;
  const entries = Object.entries(data).slice(0, maxItems);
  return (
    <div className="text-xs max-w-xs">
      {entries.map(([key, value]) => {
        const displayValue =
          typeof value === 'string' && value.length > 30
            ? value.slice(0, 30) + '...'
            : String(value);
        return (
          <div key={key} className="flex justify-between">
            <strong className="pr-1 text-gray-700">{key}:</strong>
            <span className="text-gray-900 break-all">{displayValue}</span>
          </div>
        );
      })}
      {Object.entries(data).length > maxItems && (
        <div className="text-gray-500 italic">...more</div>
      )}
    </div>
  );
};

const parseJSONSafe = (text: any): any => {
  if (!text) return null;
  if (typeof text === 'object') return text;
  try {
    let parsed = JSON.parse(text);
    if (typeof parsed === 'string') parsed = JSON.parse(parsed);
    return parsed;
  } catch {
    return null;
  }
};

const renderDetails = (detailsData: any) => {
  const parsed = parseJSONSafe(detailsData);
  if (!parsed) return <span>{typeof detailsData === 'string' ? detailsData : 'N/A'}</span>;
  return (
    <div title={JSON.stringify(parsed, null, 2)} className="cursor-help">
      <KeyValueList data={parsed} />
    </div>
  );
};

const renderPayload = (payloadData: any) => {
  const parsed = parseJSONSafe(payloadData);
  if (!parsed) return <span>{typeof payloadData === 'string' ? payloadData : 'N/A'}</span>;
  return (
    <div title={JSON.stringify(parsed, null, 2)} className="cursor-help">
      <KeyValueList data={parsed} />
    </div>
  );
};

const EditModal: React.FC<EditModalProps> = ({ transaction, isOpen, onClose, onSave }) => {
  const [payloadText, setPayloadText] = useState<string>('');

  useEffect(() => {
    if (transaction) {
      if (transaction.payload === null || transaction.payload === undefined) {
        setPayloadText('');
      } else if (typeof transaction.payload === 'string') {
        setPayloadText(transaction.payload);
      } else {
        setPayloadText(JSON.stringify(transaction.payload, null, 2));
      }
    }
  }, [transaction]);

  if (!isOpen || !transaction) return null;

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPayloadText(e.target.value);
  };

  const handleSave = () => {
    try {
      let parsed;
      try {
        parsed = JSON.parse(payloadText);
      } catch {
        parsed = payloadText;
      }
      onSave(parsed);
    } catch {
      alert('Invalid JSON format. Please fix before saving.');
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="edit-modal-title"
    >
      <div
        className="bg-white rounded-lg w-11/12 md:w-2/3 max-w-3xl p-6 relative"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 id="edit-modal-title" className="text-xl font-semibold mb-4">
          Edit Transaction ID: {transaction.id}
        </h2>
        <label className="block font-medium mb-1" htmlFor="payloadTextarea">
          Payload (JSON or text)
        </label>
        <textarea
          id="payloadTextarea"
          rows={10}
          className="w-full border border-gray-300 rounded p-2 font-mono text-sm resize-y"
          value={payloadText}
          onChange={handleChange}
          spellCheck={false}
        />
        <div className="flex justify-end mt-4 space-x-4">
          <button
            type="button"
            className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400 transition"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            type="button"
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            onClick={handleSave}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};


const AllTransactions: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [sourceFilter, setSourceFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [availableSources, setAvailableSources] = useState<string[]>([]);
  const [availableStatuses, setAvailableStatuses] = useState<string[]>(['success', 'pending', 'failed']);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError('');
      const data: Transaction[] = await apiService.getDetails();
      setTransactions(data);

      const sources = Array.from(new Set(data.map((item) => item.source)));
      setAvailableSources(sources);

      const statuses = Array.from(new Set(data.map((item) => item.status?.toLowerCase())));
      setAvailableStatuses(statuses.length > 0 ? statuses : ['success', 'pending', 'failed']);

      setCurrentPage(1);
    } catch (err) {
      setError('Failed to load transactions. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchFilteredData = async (source: string, status: string) => {
    try {
      setLoading(true);
      setError('');
      const response = await apiService.getFilteredDetails(
        source === '' ? undefined : source,
        status === '' ? undefined : status
      );
      setTransactions(response ?? []);
      setCurrentPage(1);
    } catch (err) {
      setError('Failed to filter data.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleSourceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const val = e.target.value;
    setSourceFilter(val);
    fetchFilteredData(val, statusFilter);
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const val = e.target.value;
    setStatusFilter(val);
    fetchFilteredData(sourceFilter, val);
  };

  const handleRetry = () => {
    fetchUsers();
    setSourceFilter('');
    setStatusFilter('');
  };

  const totalPages = Math.ceil(transactions.length / pageSize);
  const indexOfLast = currentPage * pageSize;
  const indexOfFirst = indexOfLast - pageSize;
  const currentTransactions = transactions.slice(indexOfFirst, indexOfLast);

  const openModal = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setSelectedTransaction(null);
    setIsModalOpen(false);
  };

  const saveUpdatedDetails = async (updatedPayload: any) => {
    if (!selectedTransaction) return;
    const payloadToSend = updatedPayload;
    const updatedTransactionData = {
      ...selectedTransaction,
      payload: payloadToSend,
    };
    try {
      await apiService.updateTransaction(selectedTransaction.id, updatedTransactionData);
      setTransactions((prev) =>
        prev.map((t) =>
          t.id === selectedTransaction.id ? { ...t, payload: payloadToSend } : t
        )
      );
      alert('Transaction updated successfully');
      closeModal();
      fetchUsers();
    } catch (error) {
      alert('Failed to update transaction, please try again.');
    }
  };

  const statusBadgeClasses = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">All Transactions</h1>
            <p className="text-gray-600 mt-1">Manage and monitor all transaction records</p>
          </div>
          {!loading && (
            <button
              onClick={handleRetry}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
              type="button"
            >
              <i className="fas fa-sync-alt text-sm mr-2" />
              <span>Refresh Data</span>
            </button>
          )}
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-6">
            <div className="flex items-center space-x-3">
              <i className="fas fa-filter text-gray-400"></i>
              <label className="text-sm font-semibold text-gray-700">Source:</label>
              <select
                value={sourceFilter}
                onChange={handleSourceChange}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[120px]"
              >
                <option value="">All Sources</option>
                {availableSources.map((src) => (
                  <option key={src} value={src} className="capitalize">{src}</option>
                ))}
              </select>
            </div>

            <div className="flex items-center space-x-3">
              <label className="text-sm font-semibold text-gray-700">Status:</label>
              <select
                value={statusFilter}
                onChange={handleStatusChange}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[120px]"
              >
                <option value="">All Statuses</option>
                {availableStatuses.map((status) => (
                  <option key={status} value={status} className="capitalize">{status}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Pagination at the top */}
        {transactions.length > 0 && !loading && !error && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            totalItems={transactions.length}
            onPageChange={setCurrentPage}
            onPageSizeChange={(size) => {
              setPageSize(size);
              setCurrentPage(1);
            }}
          />
        )}

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {loading ? (
            <LoadingSpinner size="lg" message="Loading transactions..." className="py-16" />
          ) : error ? (
            <ErrorMessage message={error} onRetry={handleRetry} />
          ) : currentTransactions.length === 0 ? (
            <EmptyState
              title="No transactions found"
              description="Try adjusting your filters or refresh the data"
            />
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-blue-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">SNO</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Source</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Details</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Payload</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Reason</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-100">
                  {currentTransactions.map((item,index) => {
                    const isFailed = item.status.toLowerCase() === 'failed';
                    const fullDetailsStr = (() => {
                      const d = parseJSONSafe(item.details);
                      return d ? JSON.stringify(d, null, 2) : (typeof item.details === 'string' ? item.details : 'N/A');
                    })();
                    const fullPayloadStr = (() => {
                      const p = parseJSONSafe(item.payload);
                      return p ? JSON.stringify(p, null, 2) : (typeof item.payload === 'string' ? item.payload : 'N/A');
                    })();
                    return (
                      <tr key={item.id} className="hover:bg-blue-50 transition-colors">
                        <td className="px-6 py-4 text-sm text-gray-800">{index+1}</td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          <a href={`mailto:${item.email}`} className="text-blue-600 hover:underline">
                            {item.email}
                          </a>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">{item.source}</td>
                        <td
                          className="px-6 py-4 text-sm text-gray-800 max-w-xs whitespace-normal cursor-help"
                          title={fullDetailsStr}
                        >
                          {renderDetails(item.details ?? 'N/A')}
                        </td>
                        <td
                          className="px-6 py-4 text-sm text-gray-800 max-w-xs whitespace-normal cursor-help"
                          title={fullPayloadStr}
                        >
                          {renderPayload(item.payload ?? 'N/A')}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">{item.reason || 'N/A'}</td>
                        <td className="px-6 py-4 text-sm">
                          <span
                            className={`
                              px-2 py-1 rounded-full text-xs font-semibold capitalize
                              ${statusBadgeClasses(item.status)}
                            `}
                          >
                            {item.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm">
                          {isFailed ? (
                            <button
                              className="text-indigo-600 hover:underline flex items-center space-x-1 whitespace-nowrap"
                              aria-label="Edit and Sync transaction"
                              onClick={() => openModal(item)}
                              type="button"
                            >
                              <i className="fas fa-edit" />
                              <i className="fas fa-sync-alt" />
                              <span>Edit &amp; Sync</span>
                            </button>
                          ) : (
                            <span className="text-gray-400 italic">—</span>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>



        <EditModal
          transaction={selectedTransaction}
          isOpen={isModalOpen}
          onClose={closeModal}
          onSave={saveUpdatedDetails}
        />
      </div>
    </Layout>
  );
};

export default AllTransactions;
