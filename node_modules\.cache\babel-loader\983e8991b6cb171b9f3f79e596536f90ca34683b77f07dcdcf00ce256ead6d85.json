{"ast": null, "code": "import axios from\"axios\";// -------------------- Configuration --------------------\nconst BASE_URL=process.env.REACT_APP_BASE_API;const COMMON_HEADERS={'Content-Type':'application/json','Accept':'application/json'};// Axios instance with base URL\nconst apiClient=axios.create({baseURL:BASE_URL,headers:COMMON_HEADERS});// -------------------- Types --------------------\n// -------------------- Auth Utilities --------------------\nexport const AUTH_STORAGE_KEY='admin_dashboard_auth';export const authUtils={isAuthenticated:()=>{const authData=localStorage.getItem(AUTH_STORAGE_KEY);if(!authData)return false;try{const parsed=JSON.parse(authData);return parsed.isAuthenticated===true;}catch{return false;}},login:credentials=>{// Enforce exact credentials validation\nif(credentials.username==='admin'&&credentials.password==='admin@123'){const authData={isAuthenticated:true,username:credentials.username,loginTime:new Date().toISOString()};localStorage.setItem(AUTH_STORAGE_KEY,JSON.stringify(authData));return{success:true,message:'Login successful',token:'mock-jwt-token'};}// Return specific error messages for better user experience\nif(credentials.username!=='admin'){return{success:false,message:'Invalid username. Please use \"admin\".'};}if(credentials.password!=='admin@123'){return{success:false,message:'Invalid password. Please use \"admin@123\".'};}return{success:false,message:'Invalid credentials. Please try again.'};},logout:()=>{localStorage.removeItem(AUTH_STORAGE_KEY);},getCurrentUser:()=>{const authData=localStorage.getItem(AUTH_STORAGE_KEY);if(!authData)return null;try{return JSON.parse(authData);}catch{return null;}}};// -------------------- API Service --------------------\nexport const apiService={async getDetails(){try{var _response$data;const response=await apiClient.get('/log/list');if(Array.isArray((_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.data)){return response.data.data;}return[];}catch(error){throw error;}},async getFilteredDetails(source,status){try{var _response$data2;const params={};if(source)params.source=source;if(status)params.status=status;const response=await apiClient.get('/log/list',{params});if(Array.isArray((_response$data2=response.data)===null||_response$data2===void 0?void 0:_response$data2.data)){return response.data.data;}return[];}catch(error){throw error;}},async updateTransaction(id,payload){try{const response=await apiClient.put(`/log/update/${id}`,payload);return response.data;}catch(error){throw error;}},async getUserById(id){try{const response=await apiClient.get(`/users/${id}`);return response.data;}catch(error){throw error;}}};export default apiService;", "map": {"version": 3, "names": ["axios", "BASE_URL", "process", "env", "REACT_APP_BASE_API", "COMMON_HEADERS", "apiClient", "create", "baseURL", "headers", "AUTH_STORAGE_KEY", "authUtils", "isAuthenticated", "authData", "localStorage", "getItem", "parsed", "JSON", "parse", "login", "credentials", "username", "password", "loginTime", "Date", "toISOString", "setItem", "stringify", "success", "message", "token", "logout", "removeItem", "getCurrentUser", "apiService", "getDetails", "_response$data", "response", "get", "Array", "isArray", "data", "error", "getFilteredDetails", "source", "status", "_response$data2", "params", "updateTransaction", "id", "payload", "put", "getUserById"], "sources": ["D:/ELGI/src/api.ts"], "sourcesContent": ["import axios from \"axios\";\n\n// -------------------- Configuration --------------------\nconst BASE_URL = process.env.REACT_APP_BASE_API;\n\nconst COMMON_HEADERS = {\n  'Content-Type': 'application/json',\n  'Accept': 'application/json',\n};\n\n// Axios instance with base URL\nconst apiClient = axios.create({\n  baseURL: BASE_URL,\n  headers: COMMON_HEADERS,\n});\n\n// -------------------- Types --------------------\nexport interface User {\n  id: number;\n  name: string;\n  username: string;\n  email: string;\n  address: {\n    street: string;\n    suite: string;\n    city: string;\n    zipcode: string;\n    geo: {\n      lat: string;\n      lng: string;\n    };\n  };\n  phone: string;\n  website: string;\n  company: {\n    name: string;\n    catchPhrase: string;\n    bs: string;\n  };\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message?: string;\n  token?: string;\n}\n\n// -------------------- Auth Utilities --------------------\nexport const AUTH_STORAGE_KEY = 'admin_dashboard_auth';\n\nexport const authUtils = {\n  isAuthenticated: (): boolean => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return false;\n    try {\n      const parsed = JSON.parse(authData);\n      return parsed.isAuthenticated === true;\n    } catch {\n      return false;\n    }\n  },\n\n  login: (credentials: LoginCredentials): AuthResponse => {\n    // Enforce exact credentials validation\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      const authData = {\n        isAuthenticated: true,\n        username: credentials.username,\n        loginTime: new Date().toISOString(),\n      };\n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));\n      return {\n        success: true,\n        message: 'Login successful',\n        token: 'mock-jwt-token'\n      };\n    }\n\n    // Return specific error messages for better user experience\n    if (credentials.username !== 'admin') {\n      return {\n        success: false,\n        message: 'Invalid username. Please use \"admin\".'\n      };\n    }\n\n    if (credentials.password !== 'admin@123') {\n      return {\n        success: false,\n        message: 'Invalid password. Please use \"admin@123\".'\n      };\n    }\n\n    return {\n      success: false,\n      message: 'Invalid credentials. Please try again.'\n    };\n  },\n\n  logout: (): void => {\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n  },\n\n  getCurrentUser: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return null;\n    try {\n      return JSON.parse(authData);\n    } catch {\n      return null;\n    }\n  }\n};\n\n// -------------------- API Service --------------------\nexport const apiService = {\n  async getDetails() {\n    try {\n      const response = await apiClient.get('/log/list');\n      if (Array.isArray(response.data?.data)) {\n        return response.data.data;\n      }\n      return [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  async getFilteredDetails(source?: string, status?: string) {\n    try {\n      const params: any = {};\n      if (source) params.source = source;\n      if (status) params.status = status;\n      const response = await apiClient.get('/log/list', { params });\n      if (Array.isArray(response.data?.data)) {\n        return response.data.data;\n      }\n      return [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  async updateTransaction(id: number, payload: any) {\n    try {\n      const response = await apiClient.put(`/log/update/${id}`, payload);\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  async getUserById(id: number): Promise<User> {\n    try {\n      const response = await apiClient.get(`/users/${id}`);\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  }\n};\n\nexport default apiService;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB;AACA,KAAM,CAAAC,QAAQ,CAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,CAE/C,KAAM,CAAAC,cAAc,CAAG,CACrB,cAAc,CAAE,kBAAkB,CAClC,QAAQ,CAAE,kBACZ,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAGN,KAAK,CAACO,MAAM,CAAC,CAC7BC,OAAO,CAAEP,QAAQ,CACjBQ,OAAO,CAAEJ,cACX,CAAC,CAAC,CAEF;AAoCA;AACA,MAAO,MAAM,CAAAK,gBAAgB,CAAG,sBAAsB,CAEtD,MAAO,MAAM,CAAAC,SAAS,CAAG,CACvBC,eAAe,CAAEA,CAAA,GAAe,CAC9B,KAAM,CAAAC,QAAQ,CAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC,CACvD,GAAI,CAACG,QAAQ,CAAE,MAAO,MAAK,CAC3B,GAAI,CACF,KAAM,CAAAG,MAAM,CAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC,CACnC,MAAO,CAAAG,MAAM,CAACJ,eAAe,GAAK,IAAI,CACxC,CAAE,KAAM,CACN,MAAO,MAAK,CACd,CACF,CAAC,CAEDO,KAAK,CAAGC,WAA6B,EAAmB,CACtD;AACA,GAAIA,WAAW,CAACC,QAAQ,GAAK,OAAO,EAAID,WAAW,CAACE,QAAQ,GAAK,WAAW,CAAE,CAC5E,KAAM,CAAAT,QAAQ,CAAG,CACfD,eAAe,CAAE,IAAI,CACrBS,QAAQ,CAAED,WAAW,CAACC,QAAQ,CAC9BE,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACDX,YAAY,CAACY,OAAO,CAAChB,gBAAgB,CAAEO,IAAI,CAACU,SAAS,CAACd,QAAQ,CAAC,CAAC,CAChE,MAAO,CACLe,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,kBAAkB,CAC3BC,KAAK,CAAE,gBACT,CAAC,CACH,CAEA;AACA,GAAIV,WAAW,CAACC,QAAQ,GAAK,OAAO,CAAE,CACpC,MAAO,CACLO,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,uCACX,CAAC,CACH,CAEA,GAAIT,WAAW,CAACE,QAAQ,GAAK,WAAW,CAAE,CACxC,MAAO,CACLM,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,2CACX,CAAC,CACH,CAEA,MAAO,CACLD,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,wCACX,CAAC,CACH,CAAC,CAEDE,MAAM,CAAEA,CAAA,GAAY,CAClBjB,YAAY,CAACkB,UAAU,CAACtB,gBAAgB,CAAC,CAC3C,CAAC,CAEDuB,cAAc,CAAEA,CAAA,GAAM,CACpB,KAAM,CAAApB,QAAQ,CAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC,CACvD,GAAI,CAACG,QAAQ,CAAE,MAAO,KAAI,CAC1B,GAAI,CACF,MAAO,CAAAI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC,CAC7B,CAAE,KAAM,CACN,MAAO,KAAI,CACb,CACF,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAqB,UAAU,CAAG,CACxB,KAAM,CAAAC,UAAUA,CAAA,CAAG,CACjB,GAAI,KAAAC,cAAA,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/B,SAAS,CAACgC,GAAG,CAAC,WAAW,CAAC,CACjD,GAAIC,KAAK,CAACC,OAAO,EAAAJ,cAAA,CAACC,QAAQ,CAACI,IAAI,UAAAL,cAAA,iBAAbA,cAAA,CAAeK,IAAI,CAAC,CAAE,CACtC,MAAO,CAAAJ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAC3B,CACA,MAAO,EAAE,CACX,CAAE,MAAOC,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkBA,CAACC,MAAe,CAAEC,MAAe,CAAE,CACzD,GAAI,KAAAC,eAAA,CACF,KAAM,CAAAC,MAAW,CAAG,CAAC,CAAC,CACtB,GAAIH,MAAM,CAAEG,MAAM,CAACH,MAAM,CAAGA,MAAM,CAClC,GAAIC,MAAM,CAAEE,MAAM,CAACF,MAAM,CAAGA,MAAM,CAClC,KAAM,CAAAR,QAAQ,CAAG,KAAM,CAAA/B,SAAS,CAACgC,GAAG,CAAC,WAAW,CAAE,CAAES,MAAO,CAAC,CAAC,CAC7D,GAAIR,KAAK,CAACC,OAAO,EAAAM,eAAA,CAACT,QAAQ,CAACI,IAAI,UAAAK,eAAA,iBAAbA,eAAA,CAAeL,IAAI,CAAC,CAAE,CACtC,MAAO,CAAAJ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAC3B,CACA,MAAO,EAAE,CACX,CAAE,MAAOC,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAM,iBAAiBA,CAACC,EAAU,CAAEC,OAAY,CAAE,CAChD,GAAI,CACF,KAAM,CAAAb,QAAQ,CAAG,KAAM,CAAA/B,SAAS,CAAC6C,GAAG,CAAC,eAAeF,EAAE,EAAE,CAAEC,OAAO,CAAC,CAClE,MAAO,CAAAb,QAAQ,CAACI,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAU,WAAWA,CAACH,EAAU,CAAiB,CAC3C,GAAI,CACF,KAAM,CAAAZ,QAAQ,CAAG,KAAM,CAAA/B,SAAS,CAACgC,GAAG,CAAC,UAAUW,EAAE,EAAE,CAAC,CACpD,MAAO,CAAAZ,QAAQ,CAACI,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CACF,CAAC,CAED,cAAe,CAAAR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}