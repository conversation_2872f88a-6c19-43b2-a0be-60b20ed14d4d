{"ast": null, "code": "import axios from \"axios\";\n\n// -------------------- Configuration --------------------\nconst BASE_URL = process.env.REACT_APP_BASE_API;\nconst COMMON_HEADERS = {\n  'Content-Type': 'application/json',\n  'Accept': 'application/json'\n};\n\n// Axios instance with base URL\nconst apiClient = axios.create({\n  baseURL: BASE_URL,\n  headers: COMMON_HEADERS\n});\n\n// -------------------- Types --------------------\n\n// -------------------- Auth Utilities --------------------\nexport const AUTH_STORAGE_KEY = 'admin_dashboard_auth';\nexport const authUtils = {\n  isAuthenticated: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return false;\n    try {\n      const parsed = JSON.parse(authData);\n      return parsed.isAuthenticated === true;\n    } catch {\n      return false;\n    }\n  },\n  login: credentials => {\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      const authData = {\n        isAuthenticated: true,\n        username: credentials.username,\n        loginTime: new Date().toISOString()\n      };\n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));\n      return {\n        success: true,\n        message: 'Login successful',\n        token: 'mock-jwt-token'\n      };\n    }\n    return {\n      success: false,\n      message: 'Invalid credentials. Please try again.'\n    };\n  },\n  logout: () => {\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n  },\n  getCurrentUser: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return null;\n    try {\n      return JSON.parse(authData);\n    } catch {\n      return null;\n    }\n  }\n};\n\n// -------------------- API Service --------------------\nexport const apiService = {\n  async getDetails() {\n    try {\n      var _response$data;\n      const response = await apiClient.get('/log/list');\n      console.log(response, \">>>>> getDetails\");\n      // Return only the array of transactions from nested data property\n      if (Array.isArray((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.data)) {\n        return response.data.data;\n      }\n      // Fallback if unexpected response shape\n      return [];\n    } catch (error) {\n      console.error('Error fetching details:', error);\n      throw error;\n    }\n  },\n  async getFilteredDetails(source) {\n    try {\n      var _response$data2;\n      const response = await apiClient.get(`/log/list`, {\n        params: {\n          source\n        }\n      });\n      // Return the filtered array similarly\n      if (Array.isArray((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.data)) {\n        return response.data.data;\n      }\n      return [];\n    } catch (error) {\n      console.error('Error fetching filtered details:', error);\n      throw error;\n    }\n  },\n  async updateTransaction(id, payload) {\n    try {\n      const response = await apiClient.put(`/log/update/${id}`, payload);\n      return response.data;\n    } catch (error) {\n      console.error(`Error updating transaction ${id}:`, error);\n      throw error;\n    }\n  },\n  async getUserById(id) {\n    try {\n      const response = await apiClient.get(`/users/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error(`Error fetching user ${id}:`, error);\n      throw error;\n    }\n  },\n  async getDashboardStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 500));\n      return {\n        google: {\n          count: 55,\n          change: 8,\n          trend: 'up'\n        },\n        tradeindia: {\n          count: 34,\n          change: 5,\n          trend: 'down'\n        },\n        indiamart: {\n          count: 42,\n          change: 12,\n          trend: 'up'\n        }\n      };\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      throw error;\n    }\n  },\n  async getRecentActivity() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 300));\n      return [{\n        id: 1,\n        message: 'New user registered',\n        time: '2 mins ago',\n        type: 'success'\n      }, {\n        id: 2,\n        message: 'Transaction completed',\n        time: '5 mins ago',\n        type: 'info'\n      }, {\n        id: 3,\n        message: 'System update available',\n        time: '1 hour ago',\n        type: 'warning'\n      }];\n    } catch (error) {\n      console.error('Error fetching recent activity:', error);\n      throw error;\n    }\n  },\n  async getQuickStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 200));\n      return {\n        totalUsers: 1234,\n        activeSessions: 89,\n        revenue: '$12,345'\n      };\n    } catch (error) {\n      console.error('Error fetching quick stats:', error);\n      throw error;\n    }\n  }\n};\n\n// For backward compatibility\nexport const api = apiService;\nexport default apiService;", "map": {"version": 3, "names": ["axios", "BASE_URL", "process", "env", "REACT_APP_BASE_API", "COMMON_HEADERS", "apiClient", "create", "baseURL", "headers", "AUTH_STORAGE_KEY", "authUtils", "isAuthenticated", "authData", "localStorage", "getItem", "parsed", "JSON", "parse", "login", "credentials", "username", "password", "loginTime", "Date", "toISOString", "setItem", "stringify", "success", "message", "token", "logout", "removeItem", "getCurrentUser", "apiService", "getDetails", "_response$data", "response", "get", "console", "log", "Array", "isArray", "data", "error", "getFilteredDetails", "source", "_response$data2", "params", "updateTransaction", "id", "payload", "put", "getUserById", "getDashboardStats", "Promise", "resolve", "setTimeout", "google", "count", "change", "trend", "tradeindia", "indiamart", "getRecentActivity", "time", "type", "getQuickStats", "totalUsers", "activeSessions", "revenue", "api"], "sources": ["D:/ELGI/src/api.ts"], "sourcesContent": ["import axios from \"axios\";\n\n// -------------------- Configuration --------------------\nconst BASE_URL = process.env.REACT_APP_BASE_API;\n\nconst COMMON_HEADERS = {\n  'Content-Type': 'application/json',\n  'Accept': 'application/json',\n};\n\n// Axios instance with base URL\nconst apiClient = axios.create({\n  baseURL: BASE_URL,\n  headers: COMMON_HEADERS,\n});\n\n// -------------------- Types --------------------\nexport interface User {\n  id: number;\n  name: string;\n  username: string;\n  email: string;\n  address: {\n    street: string;\n    suite: string;\n    city: string;\n    zipcode: string;\n    geo: {\n      lat: string;\n      lng: string;\n    };\n  };\n  phone: string;\n  website: string;\n  company: {\n    name: string;\n    catchPhrase: string;\n    bs: string;\n  };\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message?: string;\n  token?: string;\n}\n\n// -------------------- Auth Utilities --------------------\nexport const AUTH_STORAGE_KEY = 'admin_dashboard_auth';\n\nexport const authUtils = {\n  isAuthenticated: (): boolean => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return false;\n    try {\n      const parsed = JSON.parse(authData);\n      return parsed.isAuthenticated === true;\n    } catch {\n      return false;\n    }\n  },\n\n  login: (credentials: LoginCredentials): AuthResponse => {\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      const authData = {\n        isAuthenticated: true,\n        username: credentials.username,\n        loginTime: new Date().toISOString(),\n      };\n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));\n      return {\n        success: true,\n        message: 'Login successful',\n        token: 'mock-jwt-token'\n      };\n    }\n    return {\n      success: false,\n      message: 'Invalid credentials. Please try again.'\n    };\n  },\n\n  logout: (): void => {\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n  },\n\n  getCurrentUser: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return null;\n    try {\n      return JSON.parse(authData);\n    } catch {\n      return null;\n    }\n  }\n};\n\n// -------------------- API Service --------------------\nexport const apiService = {\nasync getDetails() {\n    try {\n      const response = await apiClient.get('/log/list');\n      console.log(response, \">>>>> getDetails\");\n      // Return only the array of transactions from nested data property\n      if (Array.isArray(response.data?.data)) {\n        return response.data.data;\n      }\n      // Fallback if unexpected response shape\n      return [];\n    } catch (error) {\n      console.error('Error fetching details:', error);\n      throw error;\n    }\n  },\n\n  async getFilteredDetails(source: string) {\n    try {\n      const response = await apiClient.get(`/log/list`, {\n        params: { source },\n      });\n      // Return the filtered array similarly\n      if (Array.isArray(response.data?.data)) {\n        return response.data.data;\n      }\n      return [];\n    } catch (error) {\n      console.error('Error fetching filtered details:', error);\n      throw error;\n    }\n  },\n\n  async updateTransaction(id: number, payload: any) {\n    try {\n      const response = await apiClient.put(`/log/update/${id}`, payload);\n      return response.data;\n    } catch (error) {\n      console.error(`Error updating transaction ${id}:`, error);\n      throw error;\n    }\n  },\n\n  async getUserById(id: number): Promise<User> {\n    try {\n      const response = await apiClient.get(`/users/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error(`Error fetching user ${id}:`, error);\n      throw error;\n    }\n  },\n\n  async getDashboardStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 500));\n      return {\n        google: { count: 55, change: 8, trend: 'up' as const },\n        tradeindia: { count: 34, change: 5, trend: 'down' as const },\n        indiamart: { count: 42, change: 12, trend: 'up' as const }\n      };\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      throw error;\n    }\n  },\n\n  async getRecentActivity() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 300));\n      return [\n        { id: 1, message: 'New user registered', time: '2 mins ago', type: 'success' as const },\n        { id: 2, message: 'Transaction completed', time: '5 mins ago', type: 'info' as const },\n        { id: 3, message: 'System update available', time: '1 hour ago', type: 'warning' as const }\n      ];\n    } catch (error) {\n      console.error('Error fetching recent activity:', error);\n      throw error;\n    }\n  },\n\n  async getQuickStats() {\n    try {\n      await new Promise(resolve => setTimeout(resolve, 200));\n      return {\n        totalUsers: 1234,\n        activeSessions: 89,\n        revenue: '$12,345'\n      };\n    } catch (error) {\n      console.error('Error fetching quick stats:', error);\n      throw error;\n    }\n  },\n\n};\n\n// For backward compatibility\nexport const api = apiService;\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB;AAE/C,MAAMC,cAAc,GAAG;EACrB,cAAc,EAAE,kBAAkB;EAClC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA,MAAMC,SAAS,GAAGN,KAAK,CAACO,MAAM,CAAC;EAC7BC,OAAO,EAAEP,QAAQ;EACjBQ,OAAO,EAAEJ;AACX,CAAC,CAAC;;AAEF;;AAoCA;AACA,OAAO,MAAMK,gBAAgB,GAAG,sBAAsB;AAEtD,OAAO,MAAMC,SAAS,GAAG;EACvBC,eAAe,EAAEA,CAAA,KAAe;IAC9B,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC;IACvD,IAAI,CAACG,QAAQ,EAAE,OAAO,KAAK;IAC3B,IAAI;MACF,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;MACnC,OAAOG,MAAM,CAACJ,eAAe,KAAK,IAAI;IACxC,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF,CAAC;EAEDO,KAAK,EAAGC,WAA6B,IAAmB;IACtD,IAAIA,WAAW,CAACC,QAAQ,KAAK,OAAO,IAAID,WAAW,CAACE,QAAQ,KAAK,WAAW,EAAE;MAC5E,MAAMT,QAAQ,GAAG;QACfD,eAAe,EAAE,IAAI;QACrBS,QAAQ,EAAED,WAAW,CAACC,QAAQ;QAC9BE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACDX,YAAY,CAACY,OAAO,CAAChB,gBAAgB,EAAEO,IAAI,CAACU,SAAS,CAACd,QAAQ,CAAC,CAAC;MAChE,OAAO;QACLe,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,kBAAkB;QAC3BC,KAAK,EAAE;MACT,CAAC;IACH;IACA,OAAO;MACLF,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAEDE,MAAM,EAAEA,CAAA,KAAY;IAClBjB,YAAY,CAACkB,UAAU,CAACtB,gBAAgB,CAAC;EAC3C,CAAC;EAEDuB,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAMpB,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC;IACvD,IAAI,CAACG,QAAQ,EAAE,OAAO,IAAI;IAC1B,IAAI;MACF,OAAOI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;IAC7B,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMqB,UAAU,GAAG;EAC1B,MAAMC,UAAUA,CAAA,EAAG;IACf,IAAI;MAAA,IAAAC,cAAA;MACF,MAAMC,QAAQ,GAAG,MAAM/B,SAAS,CAACgC,GAAG,CAAC,WAAW,CAAC;MACjDC,OAAO,CAACC,GAAG,CAACH,QAAQ,EAAE,kBAAkB,CAAC;MACzC;MACA,IAAII,KAAK,CAACC,OAAO,EAAAN,cAAA,GAACC,QAAQ,CAACM,IAAI,cAAAP,cAAA,uBAAbA,cAAA,CAAeO,IAAI,CAAC,EAAE;QACtC,OAAON,QAAQ,CAACM,IAAI,CAACA,IAAI;MAC3B;MACA;MACA,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMC,kBAAkBA,CAACC,MAAc,EAAE;IACvC,IAAI;MAAA,IAAAC,eAAA;MACF,MAAMV,QAAQ,GAAG,MAAM/B,SAAS,CAACgC,GAAG,CAAC,WAAW,EAAE;QAChDU,MAAM,EAAE;UAAEF;QAAO;MACnB,CAAC,CAAC;MACF;MACA,IAAIL,KAAK,CAACC,OAAO,EAAAK,eAAA,GAACV,QAAQ,CAACM,IAAI,cAAAI,eAAA,uBAAbA,eAAA,CAAeJ,IAAI,CAAC,EAAE;QACtC,OAAON,QAAQ,CAACM,IAAI,CAACA,IAAI;MAC3B;MACA,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMK,iBAAiBA,CAACC,EAAU,EAAEC,OAAY,EAAE;IAChD,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAM/B,SAAS,CAAC8C,GAAG,CAAC,eAAeF,EAAE,EAAE,EAAEC,OAAO,CAAC;MAClE,OAAOd,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,8BAA8BM,EAAE,GAAG,EAAEN,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMS,WAAWA,CAACH,EAAU,EAAiB;IAC3C,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAM/B,SAAS,CAACgC,GAAG,CAAC,UAAUY,EAAE,EAAE,CAAC;MACpD,OAAOb,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,uBAAuBM,EAAE,GAAG,EAAEN,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMU,iBAAiBA,CAAA,EAAG;IACxB,IAAI;MACF,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MACtD,OAAO;QACLE,MAAM,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAc,CAAC;QACtDC,UAAU,EAAE;UAAEH,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAgB,CAAC;QAC5DE,SAAS,EAAE;UAAEJ,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAc;MAC3D,CAAC;IACH,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMoB,iBAAiBA,CAAA,EAAG;IACxB,IAAI;MACF,MAAM,IAAIT,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MACtD,OAAO,CACL;QAAEN,EAAE,EAAE,CAAC;QAAErB,OAAO,EAAE,qBAAqB;QAAEoC,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAmB,CAAC,EACvF;QAAEhB,EAAE,EAAE,CAAC;QAAErB,OAAO,EAAE,uBAAuB;QAAEoC,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAgB,CAAC,EACtF;QAAEhB,EAAE,EAAE,CAAC;QAAErB,OAAO,EAAE,yBAAyB;QAAEoC,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAmB,CAAC,CAC5F;IACH,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMuB,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAM,IAAIZ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MACtD,OAAO;QACLY,UAAU,EAAE,IAAI;QAChBC,cAAc,EAAE,EAAE;QAClBC,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;AAEF,CAAC;;AAED;AACA,OAAO,MAAM2B,GAAG,GAAGrC,UAAU;AAC7B,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}