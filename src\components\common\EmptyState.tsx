import React from 'react';

interface EmptyStateProps {
  icon?: string;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({ 
  icon = 'fas fa-inbox',
  title, 
  description, 
  action,
  className = '' 
}) => {
  return (
    <div className={`text-center py-16 ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center">
          <i className={`${icon} text-gray-400 text-2xl`} />
        </div>
        <div>
          <p className="text-gray-500 font-medium">{title}</p>
          {description && (
            <p className="text-gray-400 text-sm mt-1">{description}</p>
          )}
          {action && (
            <button
              onClick={action.onClick}
              className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
              type="button"
            >
              {action.label}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmptyState;
