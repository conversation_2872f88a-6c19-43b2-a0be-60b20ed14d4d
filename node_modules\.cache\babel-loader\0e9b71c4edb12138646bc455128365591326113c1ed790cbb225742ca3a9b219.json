{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\common\\\\EmptyState.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EmptyState = ({\n  icon = 'fas fa-inbox',\n  title,\n  description,\n  action,\n  className = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `text-center py-16 ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: `${icon} text-gray-400 text-2xl`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 font-medium\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), description && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm mt-1\",\n          children: description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this), action && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: action.onClick,\n          className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\",\n          type: \"button\",\n          children: action.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = EmptyState;\nexport default EmptyState;\nvar _c;\n$RefreshReg$(_c, \"EmptyState\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "EmptyState", "icon", "title", "description", "action", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "label", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/common/EmptyState.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface EmptyStateProps {\n  icon?: string;\n  title: string;\n  description?: string;\n  action?: {\n    label: string;\n    onClick: () => void;\n  };\n  className?: string;\n}\n\nconst EmptyState: React.FC<EmptyStateProps> = ({ \n  icon = 'fas fa-inbox',\n  title, \n  description, \n  action,\n  className = '' \n}) => {\n  return (\n    <div className={`text-center py-16 ${className}`}>\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center\">\n          <i className={`${icon} text-gray-400 text-2xl`} />\n        </div>\n        <div>\n          <p className=\"text-gray-500 font-medium\">{title}</p>\n          {description && (\n            <p className=\"text-gray-400 text-sm mt-1\">{description}</p>\n          )}\n          {action && (\n            <button\n              onClick={action.onClick}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\"\n              type=\"button\"\n            >\n              {action.label}\n            </button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EmptyState;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa1B,MAAMC,UAAqC,GAAGA,CAAC;EAC7CC,IAAI,GAAG,cAAc;EACrBC,KAAK;EACLC,WAAW;EACXC,MAAM;EACNC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,oBACEN,OAAA;IAAKM,SAAS,EAAE,qBAAqBA,SAAS,EAAG;IAAAC,QAAA,eAC/CP,OAAA;MAAKM,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDP,OAAA;QAAKM,SAAS,EAAC,qEAAqE;QAAAC,QAAA,eAClFP,OAAA;UAAGM,SAAS,EAAE,GAAGJ,IAAI;QAA0B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNX,OAAA;QAAAO,QAAA,gBACEP,OAAA;UAAGM,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEJ;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACnDP,WAAW,iBACVJ,OAAA;UAAGM,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAEH;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC3D,EACAN,MAAM,iBACLL,OAAA;UACEY,OAAO,EAAEP,MAAM,CAACO,OAAQ;UACxBN,SAAS,EAAC,gGAAgG;UAC1GO,IAAI,EAAC,QAAQ;UAAAN,QAAA,EAEZF,MAAM,CAACS;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GA/BId,UAAqC;AAiC3C,eAAeA,UAAU;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}