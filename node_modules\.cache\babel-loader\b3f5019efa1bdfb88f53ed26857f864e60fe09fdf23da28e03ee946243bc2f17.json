{"ast": null, "code": "import axios from \"axios\";\n\n// -------------------- Configuration --------------------\nconst BASE_URL = process.env.REACT_APP_BASE_API;\nconst COMMON_HEADERS = {\n  'Content-Type': 'application/json',\n  'Accept': 'application/json'\n};\n\n// Axios instance with base URL\nconst apiClient = axios.create({\n  baseURL: BASE_URL,\n  headers: COMMON_HEADERS\n});\n\n// -------------------- Types --------------------\n\n// -------------------- Auth Utilities --------------------\nexport const AUTH_STORAGE_KEY = 'admin_dashboard_auth';\nexport const authUtils = {\n  isAuthenticated: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return false;\n    try {\n      const parsed = JSON.parse(authData);\n      return parsed.isAuthenticated === true;\n    } catch {\n      return false;\n    }\n  },\n  login: credentials => {\n    // Enforce exact credentials validation\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      const authData = {\n        isAuthenticated: true,\n        username: credentials.username,\n        loginTime: new Date().toISOString()\n      };\n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));\n      return {\n        success: true,\n        message: 'Login successful',\n        token: 'mock-jwt-token'\n      };\n    }\n\n    // Return specific error messages for better user experience\n    if (credentials.username !== 'admin') {\n      return {\n        success: false,\n        message: 'Invalid username. Please use \"admin\".'\n      };\n    }\n    if (credentials.password !== 'admin@123') {\n      return {\n        success: false,\n        message: 'Invalid password. Please use \"admin@123\".'\n      };\n    }\n    return {\n      success: false,\n      message: 'Invalid credentials. Please try again.'\n    };\n  },\n  logout: () => {\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n  },\n  getCurrentUser: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return null;\n    try {\n      return JSON.parse(authData);\n    } catch {\n      return null;\n    }\n  }\n};\n\n// -------------------- API Service --------------------\nexport const apiService = {\n  async getDetails() {\n    try {\n      var _response$data;\n      const response = await apiClient.get('/log/list');\n      if (Array.isArray((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.data)) {\n        return response.data.data;\n      }\n      return [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  async getFilteredDetails(source, status) {\n    try {\n      var _response$data2;\n      const params = {};\n      if (source) params.source = source;\n      if (status) params.status = status;\n      const response = await apiClient.get('/log/list', {\n        params\n      });\n      if (Array.isArray((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.data)) {\n        return response.data.data;\n      }\n      return [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  async updateTransaction(id, payload) {\n    try {\n      const response = await apiClient.put(`/log/update/${id}`, payload);\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  },\n  async getUserById(id) {\n    try {\n      const response = await apiClient.get(`/users/${id}`);\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  }\n};\nexport default apiService;", "map": {"version": 3, "names": ["axios", "BASE_URL", "process", "env", "REACT_APP_BASE_API", "COMMON_HEADERS", "apiClient", "create", "baseURL", "headers", "AUTH_STORAGE_KEY", "authUtils", "isAuthenticated", "authData", "localStorage", "getItem", "parsed", "JSON", "parse", "login", "credentials", "username", "password", "loginTime", "Date", "toISOString", "setItem", "stringify", "success", "message", "token", "logout", "removeItem", "getCurrentUser", "apiService", "getDetails", "_response$data", "response", "get", "Array", "isArray", "data", "error", "getFilteredDetails", "source", "status", "_response$data2", "params", "updateTransaction", "id", "payload", "put", "getUserById"], "sources": ["D:/ELGI/src/api.ts"], "sourcesContent": ["import axios from \"axios\";\n\n// -------------------- Configuration --------------------\nconst BASE_URL = process.env.REACT_APP_BASE_API;\n\nconst COMMON_HEADERS = {\n  'Content-Type': 'application/json',\n  'Accept': 'application/json',\n};\n\n// Axios instance with base URL\nconst apiClient = axios.create({\n  baseURL: BASE_URL,\n  headers: COMMON_HEADERS,\n});\n\n// -------------------- Types --------------------\nexport interface User {\n  id: number;\n  name: string;\n  username: string;\n  email: string;\n  address: {\n    street: string;\n    suite: string;\n    city: string;\n    zipcode: string;\n    geo: {\n      lat: string;\n      lng: string;\n    };\n  };\n  phone: string;\n  website: string;\n  company: {\n    name: string;\n    catchPhrase: string;\n    bs: string;\n  };\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message?: string;\n  token?: string;\n}\n\n// -------------------- Auth Utilities --------------------\nexport const AUTH_STORAGE_KEY = 'admin_dashboard_auth';\n\nexport const authUtils = {\n  isAuthenticated: (): boolean => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return false;\n    try {\n      const parsed = JSON.parse(authData);\n      return parsed.isAuthenticated === true;\n    } catch {\n      return false;\n    }\n  },\n\n  login: (credentials: LoginCredentials): AuthResponse => {\n    // Enforce exact credentials validation\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      const authData = {\n        isAuthenticated: true,\n        username: credentials.username,\n        loginTime: new Date().toISOString(),\n      };\n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));\n      return {\n        success: true,\n        message: 'Login successful',\n        token: 'mock-jwt-token'\n      };\n    }\n\n    // Return specific error messages for better user experience\n    if (credentials.username !== 'admin') {\n      return {\n        success: false,\n        message: 'Invalid username. Please use \"admin\".'\n      };\n    }\n\n    if (credentials.password !== 'admin@123') {\n      return {\n        success: false,\n        message: 'Invalid password. Please use \"admin@123\".'\n      };\n    }\n\n    return {\n      success: false,\n      message: 'Invalid credentials. Please try again.'\n    };\n  },\n\n  logout: (): void => {\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n  },\n\n  getCurrentUser: () => {\n    const authData = localStorage.getItem(AUTH_STORAGE_KEY);\n    if (!authData) return null;\n    try {\n      return JSON.parse(authData);\n    } catch {\n      return null;\n    }\n  }\n};\n\n// -------------------- API Service --------------------\nexport const apiService = {\n  async getDetails() {\n    try {\n      const response = await apiClient.get('/log/list');\n      if (Array.isArray(response.data?.data)) {\n        return response.data.data;\n      }\n      return [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  async getFilteredDetails(source?: string, status?: string) {\n    try {\n      const params: any = {};\n      if (source) params.source = source;\n      if (status) params.status = status;\n      const response = await apiClient.get('/log/list', { params });\n      if (Array.isArray(response.data?.data)) {\n        return response.data.data;\n      }\n      return [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  async updateTransaction(id: number, payload: any) {\n    try {\n      const response = await apiClient.put(`/log/update/${id}`, payload);\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  async getUserById(id: number): Promise<User> {\n    try {\n      const response = await apiClient.get(`/users/${id}`);\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  }\n};\n\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB;AAE/C,MAAMC,cAAc,GAAG;EACrB,cAAc,EAAE,kBAAkB;EAClC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA,MAAMC,SAAS,GAAGN,KAAK,CAACO,MAAM,CAAC;EAC7BC,OAAO,EAAEP,QAAQ;EACjBQ,OAAO,EAAEJ;AACX,CAAC,CAAC;;AAEF;;AAoCA;AACA,OAAO,MAAMK,gBAAgB,GAAG,sBAAsB;AAEtD,OAAO,MAAMC,SAAS,GAAG;EACvBC,eAAe,EAAEA,CAAA,KAAe;IAC9B,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC;IACvD,IAAI,CAACG,QAAQ,EAAE,OAAO,KAAK;IAC3B,IAAI;MACF,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;MACnC,OAAOG,MAAM,CAACJ,eAAe,KAAK,IAAI;IACxC,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF,CAAC;EAEDO,KAAK,EAAGC,WAA6B,IAAmB;IACtD;IACA,IAAIA,WAAW,CAACC,QAAQ,KAAK,OAAO,IAAID,WAAW,CAACE,QAAQ,KAAK,WAAW,EAAE;MAC5E,MAAMT,QAAQ,GAAG;QACfD,eAAe,EAAE,IAAI;QACrBS,QAAQ,EAAED,WAAW,CAACC,QAAQ;QAC9BE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACDX,YAAY,CAACY,OAAO,CAAChB,gBAAgB,EAAEO,IAAI,CAACU,SAAS,CAACd,QAAQ,CAAC,CAAC;MAChE,OAAO;QACLe,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,kBAAkB;QAC3BC,KAAK,EAAE;MACT,CAAC;IACH;;IAEA;IACA,IAAIV,WAAW,CAACC,QAAQ,KAAK,OAAO,EAAE;MACpC,OAAO;QACLO,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;IAEA,IAAIT,WAAW,CAACE,QAAQ,KAAK,WAAW,EAAE;MACxC,OAAO;QACLM,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;IAEA,OAAO;MACLD,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAEDE,MAAM,EAAEA,CAAA,KAAY;IAClBjB,YAAY,CAACkB,UAAU,CAACtB,gBAAgB,CAAC;EAC3C,CAAC;EAEDuB,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAMpB,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC;IACvD,IAAI,CAACG,QAAQ,EAAE,OAAO,IAAI;IAC1B,IAAI;MACF,OAAOI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;IAC7B,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMqB,UAAU,GAAG;EACxB,MAAMC,UAAUA,CAAA,EAAG;IACjB,IAAI;MAAA,IAAAC,cAAA;MACF,MAAMC,QAAQ,GAAG,MAAM/B,SAAS,CAACgC,GAAG,CAAC,WAAW,CAAC;MACjD,IAAIC,KAAK,CAACC,OAAO,EAAAJ,cAAA,GAACC,QAAQ,CAACI,IAAI,cAAAL,cAAA,uBAAbA,cAAA,CAAeK,IAAI,CAAC,EAAE;QACtC,OAAOJ,QAAQ,CAACI,IAAI,CAACA,IAAI;MAC3B;MACA,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMC,kBAAkBA,CAACC,MAAe,EAAEC,MAAe,EAAE;IACzD,IAAI;MAAA,IAAAC,eAAA;MACF,MAAMC,MAAW,GAAG,CAAC,CAAC;MACtB,IAAIH,MAAM,EAAEG,MAAM,CAACH,MAAM,GAAGA,MAAM;MAClC,IAAIC,MAAM,EAAEE,MAAM,CAACF,MAAM,GAAGA,MAAM;MAClC,MAAMR,QAAQ,GAAG,MAAM/B,SAAS,CAACgC,GAAG,CAAC,WAAW,EAAE;QAAES;MAAO,CAAC,CAAC;MAC7D,IAAIR,KAAK,CAACC,OAAO,EAAAM,eAAA,GAACT,QAAQ,CAACI,IAAI,cAAAK,eAAA,uBAAbA,eAAA,CAAeL,IAAI,CAAC,EAAE;QACtC,OAAOJ,QAAQ,CAACI,IAAI,CAACA,IAAI;MAC3B;MACA,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMM,iBAAiBA,CAACC,EAAU,EAAEC,OAAY,EAAE;IAChD,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAM/B,SAAS,CAAC6C,GAAG,CAAC,eAAeF,EAAE,EAAE,EAAEC,OAAO,CAAC;MAClE,OAAOb,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMU,WAAWA,CAACH,EAAU,EAAiB;IAC3C,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAM/B,SAAS,CAACgC,GAAG,CAAC,UAAUW,EAAE,EAAE,CAAC;MACpD,OAAOZ,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}