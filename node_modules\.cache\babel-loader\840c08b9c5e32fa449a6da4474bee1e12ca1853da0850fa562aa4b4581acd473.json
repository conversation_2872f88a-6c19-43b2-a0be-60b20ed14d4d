{"ast": null, "code": "import React from'react';import{NavLink}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Sidebar=()=>{const navItems=[{path:'/dashboard',icon:'fas fa-home',label:'Dashboard'},{path:'/transactions',icon:'fas fa-list',label:'All Transactions'}];return/*#__PURE__*/_jsx(\"div\",{className:\"w-64 bg-white shadow-sm border-r border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 mb-8\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"https://www.elgi.com/sg/wp-content/themes/ELGi/images/elgi__logo.png\",alt:\"ELGi Logo\",className:\"h-8\",onError:e=>{// Fallback to text if image fails to load\ne.currentTarget.style.display='none';const fallback=e.currentTarget.nextElementSibling;if(fallback)fallback.style.display='block';}}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden text-lg font-bold text-gray-900\",children:\"ELGi\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-xl font-bold text-gray-900\",children:\"Dashboard\"})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"space-y-2\",children:navItems.map(item=>/*#__PURE__*/_jsxs(NavLink,{to:item.path,className:_ref=>{let{isActive}=_ref;return`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${isActive?'text-white border':'text-gray-600 hover:bg-gray-50'}`;},style:_ref2=>{let{isActive}=_ref2;return isActive?{backgroundColor:'#3579F3',borderColor:'#3579F3'}:{};},children:[/*#__PURE__*/_jsx(\"i\",{className:`${item.icon} text-sm`}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:item.label})]},item.path))})]})});};export default Sidebar;", "map": {"version": 3, "names": ["React", "NavLink", "jsx", "_jsx", "jsxs", "_jsxs", "Sidebar", "navItems", "path", "icon", "label", "className", "children", "src", "alt", "onError", "e", "currentTarget", "style", "display", "fallback", "nextElement<PERSON><PERSON>ling", "map", "item", "to", "_ref", "isActive", "_ref2", "backgroundColor", "borderColor"], "sources": ["D:/ELGI/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink } from 'react-router-dom';\n\nconst Sidebar: React.FC = () => {\n  const navItems = [\n    {\n      path: '/dashboard',\n      icon: 'fas fa-home',\n      label: 'Dashboard'\n    },\n    {\n      path: '/transactions',\n      icon: 'fas fa-list',\n      label: 'All Transactions'\n    }\n  ];\n\n  return (\n    <div className=\"w-64 bg-white shadow-sm border-r border-gray-200\">\n      <div className=\"p-6\">\n        {/* Logo */}\n        <div className=\"flex items-center space-x-3 mb-8\">\n          <img\n            src=\"https://www.elgi.com/sg/wp-content/themes/ELGi/images/elgi__logo.png\"\n            alt=\"ELGi Logo\"\n            className=\"h-8\"\n            onError={(e) => {\n              // Fallback to text if image fails to load\n              e.currentTarget.style.display = 'none';\n              const fallback = e.currentTarget.nextElementSibling as HTMLElement;\n              if (fallback) fallback.style.display = 'block';\n            }}\n          />\n          <div className=\"hidden text-lg font-bold text-gray-900\">\n            ELGi\n          </div>\n          <h1 className=\"text-xl font-bold text-gray-900\">Dashboard</h1>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"space-y-2\">\n          {navItems.map((item) => (\n            <NavLink\n              key={item.path}\n              to={item.path}\n              className={({ isActive }) =>\n                `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${\n                  isActive\n                    ? 'text-white border'\n                    : 'text-gray-600 hover:bg-gray-50'\n                }`\n              }\n              style={({ isActive }) => isActive ? { backgroundColor: '#3579F3', borderColor: '#3579F3' } : {}}\n            >\n              <i className={`${item.icon} text-sm`}></i>\n              <span className=\"font-medium\">{item.label}</span>\n            </NavLink>\n          ))}\n        </nav>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3C,KAAM,CAAAC,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAC,QAAQ,CAAG,CACf,CACEC,IAAI,CAAE,YAAY,CAClBC,IAAI,CAAE,aAAa,CACnBC,KAAK,CAAE,WACT,CAAC,CACD,CACEF,IAAI,CAAE,eAAe,CACrBC,IAAI,CAAE,aAAa,CACnBC,KAAK,CAAE,kBACT,CAAC,CACF,CAED,mBACEP,IAAA,QAAKQ,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAC/DP,KAAA,QAAKM,SAAS,CAAC,KAAK,CAAAC,QAAA,eAElBP,KAAA,QAAKM,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CT,IAAA,QACEU,GAAG,CAAC,sEAAsE,CAC1EC,GAAG,CAAC,WAAW,CACfH,SAAS,CAAC,KAAK,CACfI,OAAO,CAAGC,CAAC,EAAK,CACd;AACAA,CAAC,CAACC,aAAa,CAACC,KAAK,CAACC,OAAO,CAAG,MAAM,CACtC,KAAM,CAAAC,QAAQ,CAAGJ,CAAC,CAACC,aAAa,CAACI,kBAAiC,CAClE,GAAID,QAAQ,CAAEA,QAAQ,CAACF,KAAK,CAACC,OAAO,CAAG,OAAO,CAChD,CAAE,CACH,CAAC,cACFhB,IAAA,QAAKQ,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,MAExD,CAAK,CAAC,cACNT,IAAA,OAAIQ,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,EAC3D,CAAC,cAGNT,IAAA,QAAKQ,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBL,QAAQ,CAACe,GAAG,CAAEC,IAAI,eACjBlB,KAAA,CAACJ,OAAO,EAENuB,EAAE,CAAED,IAAI,CAACf,IAAK,CACdG,SAAS,CAAEc,IAAA,MAAC,CAAEC,QAAS,CAAC,CAAAD,IAAA,OACtB,uFACEC,QAAQ,CACJ,mBAAmB,CACnB,gCAAgC,EACpC,EACH,CACDR,KAAK,CAAES,KAAA,MAAC,CAAED,QAAS,CAAC,CAAAC,KAAA,OAAK,CAAAD,QAAQ,CAAG,CAAEE,eAAe,CAAE,SAAS,CAAEC,WAAW,CAAE,SAAU,CAAC,CAAG,CAAC,CAAC,EAAC,CAAAjB,QAAA,eAEhGT,IAAA,MAAGQ,SAAS,CAAE,GAAGY,IAAI,CAACd,IAAI,UAAW,CAAI,CAAC,cAC1CN,IAAA,SAAMQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEW,IAAI,CAACb,KAAK,CAAO,CAAC,GAZ5Ca,IAAI,CAACf,IAaH,CACV,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}