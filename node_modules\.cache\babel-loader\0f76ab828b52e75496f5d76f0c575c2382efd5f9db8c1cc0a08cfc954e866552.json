{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoadingSpinner=_ref=>{let{size='md',message='Loading...',className=''}=_ref;const sizeClasses={sm:'w-6 h-6',md:'w-12 h-12',lg:'w-16 h-16'};return/*#__PURE__*/_jsxs(\"div\",{className:`flex flex-col items-center space-y-4 ${className}`,children:[/*#__PURE__*/_jsx(\"div\",{className:`${sizeClasses[size]} border-4 border-t-transparent rounded-full animate-spin`,style:{borderColor:'#3579F3'}}),message&&/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 font-medium\",children:message})]});};export default LoadingSpinner;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "LoadingSpinner", "_ref", "size", "message", "className", "sizeClasses", "sm", "md", "lg", "children", "style", "borderColor"], "sources": ["D:/ELGI/src/components/common/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  message?: string;\n  className?: string;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'md', \n  message = 'Loading...', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-12 h-12',\n    lg: 'w-16 h-16'\n  };\n\n  return (\n    <div className={`flex flex-col items-center space-y-4 ${className}`}>\n      <div\n        className={`${sizeClasses[size]} border-4 border-t-transparent rounded-full animate-spin`}\n        style={{ borderColor: '#3579F3' }}\n      />\n      {message && <p className=\"text-gray-600 font-medium\">{message}</p>}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ1B,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAIhD,IAJiD,CACrDC,IAAI,CAAG,IAAI,CACXC,OAAO,CAAG,YAAY,CACtBC,SAAS,CAAG,EACd,CAAC,CAAAH,IAAA,CACC,KAAM,CAAAI,WAAW,CAAG,CAClBC,EAAE,CAAE,SAAS,CACbC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,WACN,CAAC,CAED,mBACET,KAAA,QAAKK,SAAS,CAAE,wCAAwCA,SAAS,EAAG,CAAAK,QAAA,eAClEZ,IAAA,QACEO,SAAS,CAAE,GAAGC,WAAW,CAACH,IAAI,CAAC,0DAA2D,CAC1FQ,KAAK,CAAE,CAAEC,WAAW,CAAE,SAAU,CAAE,CACnC,CAAC,CACDR,OAAO,eAAIN,IAAA,MAAGO,SAAS,CAAC,2BAA2B,CAAAK,QAAA,CAAEN,OAAO,CAAI,CAAC,EAC/D,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}