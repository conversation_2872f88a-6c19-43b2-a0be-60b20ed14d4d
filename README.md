# React Admin Dashboard

A modern, responsive admin dashboard built with React, TypeScript, and Tailwind CSS. Features authentication, routing, API integration, and a clean professional design with optimized component architecture.

## Features

### 🔐 Authentication
- Login page with hardcoded credentials (`admin` / `admin@123`)
- Protected routes with automatic redirect
- Session management using localStorage
- Logout functionality

### 🎨 Modern UI/UX
- Professional split-screen login design with gradient backgrounds
- Responsive sidebar and header layout
- Enhanced dashboard with interactive source cards and status breakdowns
- Advanced data table with professional pagination controls
- Consistent styling with Tailwind CSS
- Font Awesome icons throughout
- Reusable UI components (LoadingSpinner, ErrorMessage, EmptyState, Pagination)

### 🛠 Technical Features
- **TypeScript** for type safety
- **React Router** for navigation
- **Context API** for state management
- **Modular component structure**
- **API service layer** with environment variables
- **Error handling** and loading states

## Project Structure

```
src/
├── api.ts                      # API service layer
├── App.tsx                     # Main app component with routing
├── index.tsx                   # Entry point
├── index.css                   # Global styles
├── contexts/
│   └── AuthContext.tsx         # Authentication context
├── components/
│   ├── ProtectedRoute.tsx      # Route protection component
│   ├── Login/
│   │   └── Login.tsx           # Login page component
│   ├── Dashboard/
│   │   └── Dashboard.tsx       # Dashboard page component
│   ├── AllTransactions/
│   │   └── AllTransactions.tsx # Transactions table component
│   └── Layout/
│       ├── Layout.tsx          # Main layout wrapper
│       ├── Header.tsx          # Header component
│       └── Sidebar.tsx         # Sidebar navigation
```

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start the development server:**
   ```bash
   npm start
   ```

3. **Open your browser:**
   Navigate to `http://localhost:3000`

### Login Credentials
- **Username:** `admin`
- **Password:** `admin@123`

## Available Scripts

- `npm start` - Runs the app in development mode
- `npm build` - Builds the app for production
- `npm test` - Launches the test runner
- `npm eject` - Ejects from Create React App (one-way operation)

## Environment Variables

Create a `.env` file in the root directory:

```env
REACT_APP_BASE_API=https://8sg83j33-3000.inc1.devtunnels.ms
```

## API Integration

The app uses JSONPlaceholder API for demo data:
- **Users endpoint:** `https://8sg83j33-3000.inc1.devtunnels.ms/users`
- **Dashboard stats:** Mock data with simulated API calls
- **Authentication:** Hardcoded credentials as specified

## Pages

### 1. Login Page
- Split-screen design (60/40 layout)
- Form validation
- Error handling
- Responsive design

### 2. Dashboard
- Platform widget cards (Facebook, Google, Twitter, Instagram)
- Recent activity feed
- Quick stats overview
- Loading states

### 3. All Transactions
- Data table with user information
- Refresh functionality
- Error handling with retry
- Responsive table design

## Technologies Used

- **React 18** - UI library
- **TypeScript** - Type safety
- **React Router v6** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Font Awesome** - Icons
- **JSONPlaceholder** - Mock API for demo data

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
