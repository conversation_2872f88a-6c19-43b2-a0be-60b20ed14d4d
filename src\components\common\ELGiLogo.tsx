import React from 'react';

interface ELGiLogoProps {
  className?: string;
  width?: number;
  height?: number;
}

const ELGiLogo: React.FC<ELGiLogoProps> = ({ 
  className = '', 
  width = 120, 
  height = 40 
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 120 40"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Black background */}
      <rect width="120" height="40" fill="#000000" rx="2" />
      
      {/* ELGi text in white */}
      <g fill="#FFFFFF">
        {/* E */}
        <rect x="8" y="8" width="12" height="3" />
        <rect x="8" y="8" width="3" height="24" />
        <rect x="8" y="18" width="10" height="3" />
        <rect x="8" y="29" width="12" height="3" />
        
        {/* L */}
        <rect x="25" y="8" width="3" height="24" />
        <rect x="25" y="29" width="12" height="3" />
        
        {/* G */}
        <rect x="42" y="8" width="12" height="3" />
        <rect x="42" y="8" width="3" height="24" />
        <rect x="42" y="29" width="12" height="3" />
        <rect x="51" y="18" width="3" height="14" />
        <rect x="48" y="18" width="6" height="3" />
        
        {/* i */}
        <rect x="59" y="8" width="3" height="3" />
        <rect x="59" y="14" width="3" height="18" />
      </g>
      
      {/* Red square accent */}
      <rect x="66" y="8" width="6" height="6" fill="#DC2626" />
      
      {/* Registered trademark symbol */}
      <g fill="#FFFFFF" fontSize="8" fontFamily="Arial, sans-serif">
        <text x="75" y="12">®</text>
      </g>
    </svg>
  );
};

export default ELGiLogo;
