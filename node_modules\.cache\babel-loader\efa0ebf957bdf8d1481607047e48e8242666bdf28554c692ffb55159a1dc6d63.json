{"ast": null, "code": "import React,{createContext,useContext,useState,useEffect}from'react';import{authUtils}from'../api';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext(undefined);export const useAuth=()=>{const context=useContext(AuthContext);if(context===undefined){throw new Error('useAuth must be used within an AuthProvider');}return context;};export const AuthProvider=_ref=>{let{children}=_ref;const[isAuthenticated,setIsAuthenticated]=useState(false);const[user,setUser]=useState(null);const[loading,setLoading]=useState(true);useEffect(()=>{// Check if user is already authenticated on app load\nconst checkAuth=()=>{const authenticated=authUtils.isAuthenticated();const currentUser=authUtils.getCurrentUser();setIsAuthenticated(authenticated);setUser(currentUser);setLoading(false);};checkAuth();},[]);const login=credentials=>{const result=authUtils.login(credentials);if(result.success){setIsAuthenticated(true);setUser(authUtils.getCurrentUser());}return result;};const logout=()=>{authUtils.logout();setIsAuthenticated(false);setUser(null);};const value={isAuthenticated,login,logout,user,loading};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authUtils", "jsx", "_jsx", "AuthContext", "undefined", "useAuth", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "isAuthenticated", "setIsAuthenticated", "user", "setUser", "loading", "setLoading", "checkAuth", "authenticated", "currentUser", "getCurrentUser", "login", "credentials", "result", "success", "logout", "value", "Provider"], "sources": ["D:/ELGI/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { authUtils, LoginCredentials, AuthResponse } from '../api';\n\ninterface AuthContextType {\n  isAuthenticated: boolean;\n  login: (credentials: LoginCredentials) => AuthResponse;\n  logout: () => void;\n  user: any;\n  loading: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);\n  const [user, setUser] = useState<any>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n\n  useEffect(() => {\n    // Check if user is already authenticated on app load\n    const checkAuth = () => {\n      const authenticated = authUtils.isAuthenticated();\n      const currentUser = authUtils.getCurrentUser();\n      \n      setIsAuthenticated(authenticated);\n      setUser(currentUser);\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, []);\n\n  const login = (credentials: LoginCredentials): AuthResponse => {\n    const result = authUtils.login(credentials);\n    \n    if (result.success) {\n      setIsAuthenticated(true);\n      setUser(authUtils.getCurrentUser());\n    }\n    \n    return result;\n  };\n\n  const logout = () => {\n    authUtils.logout();\n    setIsAuthenticated(false);\n    setUser(null);\n  };\n\n  const value: AuthContextType = {\n    isAuthenticated,\n    login,\n    logout,\n    user,\n    loading,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAmB,OAAO,CACxF,OAASC,SAAS,KAAwC,QAAQ,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAUnE,KAAM,CAAAC,WAAW,cAAGP,aAAa,CAA8BQ,SAAS,CAAC,CAEzE,MAAO,MAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGT,UAAU,CAACM,WAAW,CAAC,CACvC,GAAIG,OAAO,GAAKF,SAAS,CAAE,CACzB,KAAM,IAAI,CAAAG,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAMD,MAAO,MAAM,CAAAE,YAAyC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACpE,KAAM,CAACE,eAAe,CAAEC,kBAAkB,CAAC,CAAGd,QAAQ,CAAU,KAAK,CAAC,CACtE,KAAM,CAACe,IAAI,CAAEC,OAAO,CAAC,CAAGhB,QAAQ,CAAM,IAAI,CAAC,CAC3C,KAAM,CAACiB,OAAO,CAAEC,UAAU,CAAC,CAAGlB,QAAQ,CAAU,IAAI,CAAC,CAErDC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAkB,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAC,aAAa,CAAGlB,SAAS,CAACW,eAAe,CAAC,CAAC,CACjD,KAAM,CAAAQ,WAAW,CAAGnB,SAAS,CAACoB,cAAc,CAAC,CAAC,CAE9CR,kBAAkB,CAACM,aAAa,CAAC,CACjCJ,OAAO,CAACK,WAAW,CAAC,CACpBH,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAEDC,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAI,KAAK,CAAIC,WAA6B,EAAmB,CAC7D,KAAM,CAAAC,MAAM,CAAGvB,SAAS,CAACqB,KAAK,CAACC,WAAW,CAAC,CAE3C,GAAIC,MAAM,CAACC,OAAO,CAAE,CAClBZ,kBAAkB,CAAC,IAAI,CAAC,CACxBE,OAAO,CAACd,SAAS,CAACoB,cAAc,CAAC,CAAC,CAAC,CACrC,CAEA,MAAO,CAAAG,MAAM,CACf,CAAC,CAED,KAAM,CAAAE,MAAM,CAAGA,CAAA,GAAM,CACnBzB,SAAS,CAACyB,MAAM,CAAC,CAAC,CAClBb,kBAAkB,CAAC,KAAK,CAAC,CACzBE,OAAO,CAAC,IAAI,CAAC,CACf,CAAC,CAED,KAAM,CAAAY,KAAsB,CAAG,CAC7Bf,eAAe,CACfU,KAAK,CACLI,MAAM,CACNZ,IAAI,CACJE,OACF,CAAC,CAED,mBACEb,IAAA,CAACC,WAAW,CAACwB,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAhB,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}