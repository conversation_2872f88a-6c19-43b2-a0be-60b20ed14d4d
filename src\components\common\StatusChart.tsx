import React from 'react';

interface StatusData {
  success: number;
  pending: number;
  failed: number;
}

interface StatusChartProps {
  data: StatusData;
  className?: string;
}

const StatusChart: React.FC<StatusChartProps> = ({ data, className = '' }) => {
  const total = data.success + data.pending + data.failed;
  
  if (total === 0) {
    return (
      <div className={`flex items-center justify-center h-48 ${className}`}>
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  const successPercentage = (data.success / total) * 100;
  const pendingPercentage = (data.pending / total) * 100;
  const failedPercentage = (data.failed / total) * 100;

  // Donut chart implementation
  const radius = 70;
  const strokeWidth = 20;
  const normalizedRadius = radius - strokeWidth * 0.5;
  const circumference = normalizedRadius * 2 * Math.PI;

  const successStrokeDasharray = `${(successPercentage / 100) * circumference} ${circumference}`;
  const pendingStrokeDasharray = `${(pendingPercentage / 100) * circumference} ${circumference}`;
  const failedStrokeDasharray = `${(failedPercentage / 100) * circumference} ${circumference}`;

  const successOffset = 0;
  const pendingOffset = -(successPercentage / 100) * circumference;
  const failedOffset = -((successPercentage + pendingPercentage) / 100) * circumference;

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div className="relative">
        <svg
          height={radius * 2}
          width={radius * 2}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            stroke="#f3f4f6"
            fill="transparent"
            strokeWidth={strokeWidth}
            r={normalizedRadius}
            cx={radius}
            cy={radius}
          />
          
          {/* Success arc */}
          {data.success > 0 && (
            <circle
              stroke="#10b981"
              fill="transparent"
              strokeWidth={strokeWidth}
              strokeDasharray={successStrokeDasharray}
              strokeDashoffset={successOffset}
              strokeLinecap="round"
              r={normalizedRadius}
              cx={radius}
              cy={radius}
              className="transition-all duration-500"
            />
          )}
          
          {/* Pending arc */}
          {data.pending > 0 && (
            <circle
              stroke="#f59e0b"
              fill="transparent"
              strokeWidth={strokeWidth}
              strokeDasharray={pendingStrokeDasharray}
              strokeDashoffset={pendingOffset}
              strokeLinecap="round"
              r={normalizedRadius}
              cx={radius}
              cy={radius}
              className="transition-all duration-500"
            />
          )}
          
          {/* Failed arc */}
          {data.failed > 0 && (
            <circle
              stroke="#ef4444"
              fill="transparent"
              strokeWidth={strokeWidth}
              strokeDasharray={failedStrokeDasharray}
              strokeDashoffset={failedOffset}
              strokeLinecap="round"
              r={normalizedRadius}
              cx={radius}
              cy={radius}
              className="transition-all duration-500"
            />
          )}
        </svg>
        
        {/* Center text */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className="text-2xl font-bold text-gray-900">{total}</span>
          <span className="text-sm text-gray-500">Total</span>
        </div>
      </div>
      
      {/* Legend */}
      <div className="mt-4 space-y-2">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-sm text-gray-700">Success ({successPercentage.toFixed(1)}%)</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <span className="text-sm text-gray-700">Pending ({pendingPercentage.toFixed(1)}%)</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <span className="text-sm text-gray-700">Failed ({failedPercentage.toFixed(1)}%)</span>
        </div>
      </div>
    </div>
  );
};

export default StatusChart;
