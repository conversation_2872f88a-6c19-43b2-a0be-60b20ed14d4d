{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\AllTransactions\\\\AllTransactions.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Layout from '../Layout/Layout';\nimport { apiService } from '../../api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllTransactions = () => {\n  _s();\n  const [transactions, setTransactions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [sourceFilter, setSourceFilter] = useState('');\n  const [availableSources, setAvailableSources] = useState([]);\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await apiService.getDetails();\n      const data = (response === null || response === void 0 ? void 0 : response.data) || [];\n      setTransactions(data);\n\n      // Collect unique sources\n      const sources = Array.from(new Set(data.map(item => item.source)));\n      setAvailableSources(sources);\n    } catch (err) {\n      console.error('Error fetching users:', err);\n      setError('Failed to load transactions. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchFiltered = async source => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await apiService.getFilteredDetails(source);\n      setTransactions((response === null || response === void 0 ? void 0 : response.data) || []);\n    } catch (err) {\n      console.error('Error fetching filtered data:', err);\n      setError('Failed to filter data.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n  const handleSourceChange = e => {\n    const value = e.target.value;\n    setSourceFilter(value);\n    if (value === '') {\n      fetchUsers();\n    } else {\n      fetchFiltered(value);\n    }\n  };\n  const handleRetry = () => {\n    fetchUsers();\n    setSourceFilter('');\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"All Transactions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRetry,\n          className: \"px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors flex items-center space-x-2\",\n          style: {\n            backgroundColor: '#3579F3'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-sync-alt text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Refresh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"text-sm font-medium text-gray-700\",\n          children: \"Filter by Source:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: sourceFilter,\n          onChange: handleSourceChange,\n          className: \"border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), availableSources.map(source => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: source,\n            children: source\n          }, source, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 border-4 border-t-transparent rounded-full animate-spin mx-auto mb-4\",\n            style: {\n              borderColor: '#3579F3'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Loading transactions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-exclamation-triangle text-red-600 text-xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-600 mb-4\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRetry,\n            className: \"px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors\",\n            style: {\n              backgroundColor: '#3579F3'\n            },\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this) : transactions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-inbox text-gray-400 text-xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"No transactions found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              style: {\n                backgroundColor: '#f8fafc'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\",\n                  children: \"Source\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\",\n                  children: \"Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\",\n                  children: \"Reason\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\",\n                  children: \"Action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-100\",\n              children: transactions.map(item => {\n                var _parsed$message, _parsed$message2;\n                let parsed = {};\n                try {\n                  parsed = JSON.parse(item.details);\n                } catch {\n                  console.error('Invalid JSON in details:', item.id);\n                }\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"hover:bg-blue-50 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 text-sm text-gray-800\",\n                    children: parsed.sender_name || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 text-sm text-gray-800\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: `mailto:${parsed.sender_email || item.email}`,\n                      className: \"text-blue-600 hover:underline\",\n                      children: parsed.sender_email || item.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 text-sm text-gray-800\",\n                    children: item.source\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 text-sm text-gray-800 max-w-xs truncate\",\n                    title: ((_parsed$message = parsed.message) === null || _parsed$message === void 0 ? void 0 : _parsed$message.replace(/(<([^>]+)>)/gi, '')) || 'N/A',\n                    children: [((_parsed$message2 = parsed.message) === null || _parsed$message2 === void 0 ? void 0 : _parsed$message2.replace(/(<([^>]+)>)/gi, '').slice(0, 80)) || 'N/A', \"...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 text-sm text-gray-800\",\n                    children: item.reason || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 text-sm text-gray-800 capitalize\",\n                    children: item.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 text-sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"text-blue-500 hover:underline\",\n                      children: \"View\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 25\n                  }, this)]\n                }, item.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), transactions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 text-sm text-gray-600\",\n        children: [\"Showing \", transactions.length, \" transaction\", transactions.length !== 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(AllTransactions, \"CaVziO9h5xWU7FxtH5exJltuIIg=\");\n_c = AllTransactions;\nexport default AllTransactions;\nvar _c;\n$RefreshReg$(_c, \"AllTransactions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "apiService", "jsxDEV", "_jsxDEV", "AllTransactions", "_s", "transactions", "setTransactions", "loading", "setLoading", "error", "setError", "sourceFilter", "setSourceFilter", "availableSources", "setAvailableSources", "fetchUsers", "response", "getDetails", "data", "sources", "Array", "from", "Set", "map", "item", "source", "err", "console", "fetchFiltered", "getFilteredDetails", "handleSourceChange", "e", "value", "target", "handleRetry", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "backgroundColor", "onChange", "borderColor", "length", "_parsed$message", "_parsed$message2", "parsed", "JSON", "parse", "details", "id", "sender_name", "href", "sender_email", "email", "title", "message", "replace", "slice", "reason", "status", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/AllTransactions/AllTransactions.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Layout from '../Layout/Layout';\nimport { apiService } from '../../api';\n\ninterface Transaction {\n  id: number;\n  email: string;\n  details: string;\n  status: string;\n  reason: string | null;\n  source: string;\n  created_at: string;\n}\n\ninterface ParsedDetails {\n  sender_name?: string;\n  sender_email?: string;\n  message?: string;\n}\n\nconst AllTransactions: React.FC = () => {\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [sourceFilter, setSourceFilter] = useState<string>('');\n  const [availableSources, setAvailableSources] = useState<string[]>([]);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await apiService.getDetails();\n      const data = response?.data || [];\n      setTransactions(data);\n\n      // Collect unique sources\n      const sources = Array.from(new Set(data.map((item: Transaction) => item.source)));\n      setAvailableSources(sources);\n    } catch (err) {\n      console.error('Error fetching users:', err);\n      setError('Failed to load transactions. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchFiltered = async (source: string) => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await apiService.getFilteredDetails(source);\n      setTransactions(response?.data || []);\n    } catch (err) {\n      console.error('Error fetching filtered data:', err);\n      setError('Failed to filter data.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n\n  const handleSourceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const value = e.target.value;\n    setSourceFilter(value);\n    if (value === '') {\n      fetchUsers();\n    } else {\n      fetchFiltered(value);\n    }\n  };\n\n  const handleRetry = () => {\n    fetchUsers();\n    setSourceFilter('');\n  };\n\n  return (\n    <Layout>\n      <div>\n        <div className=\"flex justify-between items-center mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">All Transactions</h1>\n          {!loading && (\n            <button\n              onClick={handleRetry}\n              className=\"px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors flex items-center space-x-2\"\n              style={{ backgroundColor: '#3579F3' }}\n            >\n              <i className=\"fas fa-sync-alt text-sm\"></i>\n              <span>Refresh</span>\n            </button>\n          )}\n        </div>\n\n        {/* Filter Dropdown */}\n        <div className=\"mb-4 flex items-center space-x-4\">\n          <label className=\"text-sm font-medium text-gray-700\">Filter by Source:</label>\n          <select\n            value={sourceFilter}\n            onChange={handleSourceChange}\n            className=\"border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none\"\n          >\n            <option value=\"\">All</option>\n            {availableSources.map((source) => (\n              <option key={source} value={source}>\n                {source}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n          {loading ? (\n            <div className=\"text-center py-12\">\n              <div className=\"w-8 h-8 border-4 border-t-transparent rounded-full animate-spin mx-auto mb-4\" style={{ borderColor: '#3579F3' }}></div>\n              <p className=\"text-gray-600\">Loading transactions...</p>\n            </div>\n          ) : error ? (\n            <div className=\"text-center py-12\">\n              <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <i className=\"fas fa-exclamation-triangle text-red-600 text-xl\"></i>\n              </div>\n              <p className=\"text-red-600 mb-4\">{error}</p>\n              <button\n                onClick={handleRetry}\n                className=\"px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors\"\n                style={{ backgroundColor: '#3579F3' }}\n              >\n                Try Again\n              </button>\n            </div>\n          ) : transactions.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <i className=\"fas fa-inbox text-gray-400 text-xl\"></i>\n              </div>\n              <p className=\"text-gray-500\">No transactions found</p>\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead style={{ backgroundColor: '#f8fafc' }}>\n                  <tr>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\">Name</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\">Email</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\">Source</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\">Details</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\">Reason</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\">Status</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider\">Action</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-100\">\n                  {transactions.map((item) => {\n                    let parsed: ParsedDetails = {};\n                    try {\n                      parsed = JSON.parse(item.details);\n                    } catch {\n                      console.error('Invalid JSON in details:', item.id);\n                    }\n\n                    return (\n                      <tr key={item.id} className=\"hover:bg-blue-50 transition-colors\">\n                        <td className=\"px-6 py-4 text-sm text-gray-800\">{parsed.sender_name || 'N/A'}</td>\n                        <td className=\"px-6 py-4 text-sm text-gray-800\">\n                          <a\n                            href={`mailto:${parsed.sender_email || item.email}`}\n                            className=\"text-blue-600 hover:underline\"\n                          >\n                            {parsed.sender_email || item.email}\n                          </a>\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-800\">{item.source}</td>\n                        <td\n                          className=\"px-6 py-4 text-sm text-gray-800 max-w-xs truncate\"\n                          title={parsed.message?.replace(/(<([^>]+)>)/gi, '') || 'N/A'}\n                        >\n                          {parsed.message?.replace(/(<([^>]+)>)/gi, '').slice(0, 80) || 'N/A'}...\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-800\">{item.reason || 'N/A'}</td>\n                        <td className=\"px-6 py-4 text-sm text-gray-800 capitalize\">{item.status}</td>\n                        <td className=\"px-6 py-4 text-sm\">\n                          <button className=\"text-blue-500 hover:underline\">View</button>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n\n        {transactions.length > 0 && (\n          <div className=\"mt-4 text-sm text-gray-600\">\n            Showing {transactions.length} transaction{transactions.length !== 1 ? 's' : ''}\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default AllTransactions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,UAAU,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkBvC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAW,EAAE,CAAC;EAEtE,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMM,QAAQ,GAAG,MAAMhB,UAAU,CAACiB,UAAU,CAAC,CAAC;MAC9C,MAAMC,IAAI,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,KAAI,EAAE;MACjCZ,eAAe,CAACY,IAAI,CAAC;;MAErB;MACA,MAAMC,OAAO,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACJ,IAAI,CAACK,GAAG,CAAEC,IAAiB,IAAKA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;MACjFX,mBAAmB,CAACK,OAAO,CAAC;IAC9B,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,uBAAuB,EAAEiB,GAAG,CAAC;MAC3ChB,QAAQ,CAAC,gDAAgD,CAAC;IAC5D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,aAAa,GAAG,MAAOH,MAAc,IAAK;IAC9C,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMM,QAAQ,GAAG,MAAMhB,UAAU,CAAC6B,kBAAkB,CAACJ,MAAM,CAAC;MAC5DnB,eAAe,CAAC,CAAAU,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,KAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,+BAA+B,EAAEiB,GAAG,CAAC;MACnDhB,QAAQ,CAAC,wBAAwB,CAAC;IACpC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDV,SAAS,CAAC,MAAM;IACdiB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,kBAAkB,GAAIC,CAAuC,IAAK;IACtE,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BpB,eAAe,CAACoB,KAAK,CAAC;IACtB,IAAIA,KAAK,KAAK,EAAE,EAAE;MAChBjB,UAAU,CAAC,CAAC;IACd,CAAC,MAAM;MACLa,aAAa,CAACI,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBnB,UAAU,CAAC,CAAC;IACZH,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,oBACEV,OAAA,CAACH,MAAM;IAAAoC,QAAA,eACLjC,OAAA;MAAAiC,QAAA,gBACEjC,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAD,QAAA,gBACrDjC,OAAA;UAAIkC,SAAS,EAAC,kCAAkC;UAAAD,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACrE,CAACjC,OAAO,iBACPL,OAAA;UACEuC,OAAO,EAAEP,WAAY;UACrBE,SAAS,EAAC,gGAAgG;UAC1GM,KAAK,EAAE;YAAEC,eAAe,EAAE;UAAU,CAAE;UAAAR,QAAA,gBAEtCjC,OAAA;YAAGkC,SAAS,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CtC,OAAA;YAAAiC,QAAA,EAAM;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtC,OAAA;QAAKkC,SAAS,EAAC,kCAAkC;QAAAD,QAAA,gBAC/CjC,OAAA;UAAOkC,SAAS,EAAC,mCAAmC;UAAAD,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9EtC,OAAA;UACE8B,KAAK,EAAErB,YAAa;UACpBiC,QAAQ,EAAEd,kBAAmB;UAC7BM,SAAS,EAAC,qEAAqE;UAAAD,QAAA,gBAE/EjC,OAAA;YAAQ8B,KAAK,EAAC,EAAE;YAAAG,QAAA,EAAC;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAC5B3B,gBAAgB,CAACU,GAAG,CAAEE,MAAM,iBAC3BvB,OAAA;YAAqB8B,KAAK,EAAEP,MAAO;YAAAU,QAAA,EAChCV;UAAM,GADIA,MAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEX,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtC,OAAA;QAAKkC,SAAS,EAAC,sEAAsE;QAAAD,QAAA,EAClF5B,OAAO,gBACNL,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAChCjC,OAAA;YAAKkC,SAAS,EAAC,8EAA8E;YAACM,KAAK,EAAE;cAAEG,WAAW,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvItC,OAAA;YAAGkC,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,GACJ/B,KAAK,gBACPP,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAChCjC,OAAA;YAAKkC,SAAS,EAAC,iFAAiF;YAAAD,QAAA,eAC9FjC,OAAA;cAAGkC,SAAS,EAAC;YAAkD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACNtC,OAAA;YAAGkC,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAE1B;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CtC,OAAA;YACEuC,OAAO,EAAEP,WAAY;YACrBE,SAAS,EAAC,oEAAoE;YAC9EM,KAAK,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAAR,QAAA,EACvC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,GACJnC,YAAY,CAACyC,MAAM,KAAK,CAAC,gBAC3B5C,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAChCjC,OAAA;YAAKkC,SAAS,EAAC,kFAAkF;YAAAD,QAAA,eAC/FjC,OAAA;cAAGkC,SAAS,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNtC,OAAA;YAAGkC,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,gBAENtC,OAAA;UAAKkC,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9BjC,OAAA;YAAOkC,SAAS,EAAC,qCAAqC;YAAAD,QAAA,gBACpDjC,OAAA;cAAOwC,KAAK,EAAE;gBAAEC,eAAe,EAAE;cAAU,CAAE;cAAAR,QAAA,eAC3CjC,OAAA;gBAAAiC,QAAA,gBACEjC,OAAA;kBAAIkC,SAAS,EAAC,kFAAkF;kBAAAD,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1GtC,OAAA;kBAAIkC,SAAS,EAAC,kFAAkF;kBAAAD,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3GtC,OAAA;kBAAIkC,SAAS,EAAC,kFAAkF;kBAAAD,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5GtC,OAAA;kBAAIkC,SAAS,EAAC,kFAAkF;kBAAAD,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7GtC,OAAA;kBAAIkC,SAAS,EAAC,kFAAkF;kBAAAD,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5GtC,OAAA;kBAAIkC,SAAS,EAAC,kFAAkF;kBAAAD,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5GtC,OAAA;kBAAIkC,SAAS,EAAC,kFAAkF;kBAAAD,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRtC,OAAA;cAAOkC,SAAS,EAAC,mCAAmC;cAAAD,QAAA,EACjD9B,YAAY,CAACkB,GAAG,CAAEC,IAAI,IAAK;gBAAA,IAAAuB,eAAA,EAAAC,gBAAA;gBAC1B,IAAIC,MAAqB,GAAG,CAAC,CAAC;gBAC9B,IAAI;kBACFA,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC3B,IAAI,CAAC4B,OAAO,CAAC;gBACnC,CAAC,CAAC,MAAM;kBACNzB,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAEe,IAAI,CAAC6B,EAAE,CAAC;gBACpD;gBAEA,oBACEnD,OAAA;kBAAkBkC,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,gBAC9DjC,OAAA;oBAAIkC,SAAS,EAAC,iCAAiC;oBAAAD,QAAA,EAAEc,MAAM,CAACK,WAAW,IAAI;kBAAK;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClFtC,OAAA;oBAAIkC,SAAS,EAAC,iCAAiC;oBAAAD,QAAA,eAC7CjC,OAAA;sBACEqD,IAAI,EAAE,UAAUN,MAAM,CAACO,YAAY,IAAIhC,IAAI,CAACiC,KAAK,EAAG;sBACpDrB,SAAS,EAAC,+BAA+B;sBAAAD,QAAA,EAExCc,MAAM,CAACO,YAAY,IAAIhC,IAAI,CAACiC;oBAAK;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLtC,OAAA;oBAAIkC,SAAS,EAAC,iCAAiC;oBAAAD,QAAA,EAAEX,IAAI,CAACC;kBAAM;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClEtC,OAAA;oBACEkC,SAAS,EAAC,mDAAmD;oBAC7DsB,KAAK,EAAE,EAAAX,eAAA,GAAAE,MAAM,CAACU,OAAO,cAAAZ,eAAA,uBAAdA,eAAA,CAAgBa,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,KAAI,KAAM;oBAAAzB,QAAA,GAE5D,EAAAa,gBAAA,GAAAC,MAAM,CAACU,OAAO,cAAAX,gBAAA,uBAAdA,gBAAA,CAAgBY,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAI,KAAK,EAAC,KACtE;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLtC,OAAA;oBAAIkC,SAAS,EAAC,iCAAiC;oBAAAD,QAAA,EAAEX,IAAI,CAACsC,MAAM,IAAI;kBAAK;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3EtC,OAAA;oBAAIkC,SAAS,EAAC,4CAA4C;oBAAAD,QAAA,EAAEX,IAAI,CAACuC;kBAAM;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7EtC,OAAA;oBAAIkC,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,eAC/BjC,OAAA;sBAAQkC,SAAS,EAAC,+BAA+B;sBAAAD,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA,GArBEhB,IAAI,CAAC6B,EAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsBZ,CAAC;cAET,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELnC,YAAY,CAACyC,MAAM,GAAG,CAAC,iBACtB5C,OAAA;QAAKkC,SAAS,EAAC,4BAA4B;QAAAD,QAAA,GAAC,UAClC,EAAC9B,YAAY,CAACyC,MAAM,EAAC,cAAY,EAACzC,YAAY,CAACyC,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACpC,EAAA,CAvLID,eAAyB;AAAA6D,EAAA,GAAzB7D,eAAyB;AAyL/B,eAAeA,eAAe;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}