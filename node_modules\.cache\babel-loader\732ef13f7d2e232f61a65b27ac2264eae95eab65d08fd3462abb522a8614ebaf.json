{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatusChart=_ref=>{let{data,className=''}=_ref;const total=data.success+data.pending+data.failed;if(total===0){return/*#__PURE__*/_jsx(\"div\",{className:`flex items-center justify-center h-48 ${className}`,children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500\",children:\"No data available\"})});}const successPercentage=data.success/total*100;const pendingPercentage=data.pending/total*100;const failedPercentage=data.failed/total*100;// Donut chart implementation\nconst radius=70;const strokeWidth=20;const normalizedRadius=radius-strokeWidth*0.5;const circumference=normalizedRadius*2*Math.PI;const successStrokeDasharray=`${successPercentage/100*circumference} ${circumference}`;const pendingStrokeDasharray=`${pendingPercentage/100*circumference} ${circumference}`;const failedStrokeDasharray=`${failedPercentage/100*circumference} ${circumference}`;const successOffset=0;const pendingOffset=-(successPercentage/100)*circumference;const failedOffset=-((successPercentage+pendingPercentage)/100)*circumference;return/*#__PURE__*/_jsxs(\"div\",{className:`flex flex-col items-center ${className}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsxs(\"svg\",{height:radius*2,width:radius*2,className:\"transform -rotate-90\",children:[/*#__PURE__*/_jsx(\"circle\",{stroke:\"#f3f4f6\",fill:\"transparent\",strokeWidth:strokeWidth,r:normalizedRadius,cx:radius,cy:radius}),data.success>0&&/*#__PURE__*/_jsx(\"circle\",{stroke:\"#10b981\",fill:\"transparent\",strokeWidth:strokeWidth,strokeDasharray:successStrokeDasharray,strokeDashoffset:successOffset,strokeLinecap:\"round\",r:normalizedRadius,cx:radius,cy:radius,className:\"transition-all duration-500\"}),data.pending>0&&/*#__PURE__*/_jsx(\"circle\",{stroke:\"#f59e0b\",fill:\"transparent\",strokeWidth:strokeWidth,strokeDasharray:pendingStrokeDasharray,strokeDashoffset:pendingOffset,strokeLinecap:\"round\",r:normalizedRadius,cx:radius,cy:radius,className:\"transition-all duration-500\"}),data.failed>0&&/*#__PURE__*/_jsx(\"circle\",{stroke:\"#ef4444\",fill:\"transparent\",strokeWidth:strokeWidth,strokeDasharray:failedStrokeDasharray,strokeDashoffset:failedOffset,strokeLinecap:\"round\",r:normalizedRadius,cx:radius,cy:radius,className:\"transition-all duration-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl font-bold text-gray-900\",children:total}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-500\",children:\"Total\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-green-500 rounded-full\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-700\",children:[\"Success (\",successPercentage.toFixed(1),\"%)\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-yellow-500 rounded-full\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-700\",children:[\"Pending (\",pendingPercentage.toFixed(1),\"%)\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-red-500 rounded-full\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-700\",children:[\"Failed (\",failedPercentage.toFixed(1),\"%)\"]})]})]})]});};export default StatusChart;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "StatusChart", "_ref", "data", "className", "total", "success", "pending", "failed", "children", "successPercentage", "pendingPercentage", "failedPercentage", "radius", "strokeWidth", "normalizedRadius", "circumference", "Math", "PI", "successStrokeDasharray", "pendingStrokeDasharray", "failedStrokeDasharray", "successOffset", "pendingOffset", "failedOffset", "height", "width", "stroke", "fill", "r", "cx", "cy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeLinecap", "toFixed"], "sources": ["D:/ELGI/src/components/common/StatusChart.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface StatusData {\n  success: number;\n  pending: number;\n  failed: number;\n}\n\ninterface StatusChartProps {\n  data: StatusData;\n  className?: string;\n}\n\nconst StatusChart: React.FC<StatusChartProps> = ({ data, className = '' }) => {\n  const total = data.success + data.pending + data.failed;\n  \n  if (total === 0) {\n    return (\n      <div className={`flex items-center justify-center h-48 ${className}`}>\n        <p className=\"text-gray-500\">No data available</p>\n      </div>\n    );\n  }\n\n  const successPercentage = (data.success / total) * 100;\n  const pendingPercentage = (data.pending / total) * 100;\n  const failedPercentage = (data.failed / total) * 100;\n\n  // Donut chart implementation\n  const radius = 70;\n  const strokeWidth = 20;\n  const normalizedRadius = radius - strokeWidth * 0.5;\n  const circumference = normalizedRadius * 2 * Math.PI;\n\n  const successStrokeDasharray = `${(successPercentage / 100) * circumference} ${circumference}`;\n  const pendingStrokeDasharray = `${(pendingPercentage / 100) * circumference} ${circumference}`;\n  const failedStrokeDasharray = `${(failedPercentage / 100) * circumference} ${circumference}`;\n\n  const successOffset = 0;\n  const pendingOffset = -(successPercentage / 100) * circumference;\n  const failedOffset = -((successPercentage + pendingPercentage) / 100) * circumference;\n\n  return (\n    <div className={`flex flex-col items-center ${className}`}>\n      <div className=\"relative\">\n        <svg\n          height={radius * 2}\n          width={radius * 2}\n          className=\"transform -rotate-90\"\n        >\n          {/* Background circle */}\n          <circle\n            stroke=\"#f3f4f6\"\n            fill=\"transparent\"\n            strokeWidth={strokeWidth}\n            r={normalizedRadius}\n            cx={radius}\n            cy={radius}\n          />\n          \n          {/* Success arc */}\n          {data.success > 0 && (\n            <circle\n              stroke=\"#10b981\"\n              fill=\"transparent\"\n              strokeWidth={strokeWidth}\n              strokeDasharray={successStrokeDasharray}\n              strokeDashoffset={successOffset}\n              strokeLinecap=\"round\"\n              r={normalizedRadius}\n              cx={radius}\n              cy={radius}\n              className=\"transition-all duration-500\"\n            />\n          )}\n          \n          {/* Pending arc */}\n          {data.pending > 0 && (\n            <circle\n              stroke=\"#f59e0b\"\n              fill=\"transparent\"\n              strokeWidth={strokeWidth}\n              strokeDasharray={pendingStrokeDasharray}\n              strokeDashoffset={pendingOffset}\n              strokeLinecap=\"round\"\n              r={normalizedRadius}\n              cx={radius}\n              cy={radius}\n              className=\"transition-all duration-500\"\n            />\n          )}\n          \n          {/* Failed arc */}\n          {data.failed > 0 && (\n            <circle\n              stroke=\"#ef4444\"\n              fill=\"transparent\"\n              strokeWidth={strokeWidth}\n              strokeDasharray={failedStrokeDasharray}\n              strokeDashoffset={failedOffset}\n              strokeLinecap=\"round\"\n              r={normalizedRadius}\n              cx={radius}\n              cy={radius}\n              className=\"transition-all duration-500\"\n            />\n          )}\n        </svg>\n        \n        {/* Center text */}\n        <div className=\"absolute inset-0 flex flex-col items-center justify-center\">\n          <span className=\"text-2xl font-bold text-gray-900\">{total}</span>\n          <span className=\"text-sm text-gray-500\">Total</span>\n        </div>\n      </div>\n      \n      {/* Legend */}\n      <div className=\"mt-4 space-y-2\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n          <span className=\"text-sm text-gray-700\">Success ({successPercentage.toFixed(1)}%)</span>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n          <span className=\"text-sm text-gray-700\">Pending ({pendingPercentage.toFixed(1)}%)</span>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n          <span className=\"text-sm text-gray-700\">Failed ({failedPercentage.toFixed(1)}%)</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StatusChart;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAa1B,KAAM,CAAAC,WAAuC,CAAGC,IAAA,EAA8B,IAA7B,CAAEC,IAAI,CAAEC,SAAS,CAAG,EAAG,CAAC,CAAAF,IAAA,CACvE,KAAM,CAAAG,KAAK,CAAGF,IAAI,CAACG,OAAO,CAAGH,IAAI,CAACI,OAAO,CAAGJ,IAAI,CAACK,MAAM,CAEvD,GAAIH,KAAK,GAAK,CAAC,CAAE,CACf,mBACEP,IAAA,QAAKM,SAAS,CAAE,yCAAyCA,SAAS,EAAG,CAAAK,QAAA,cACnEX,IAAA,MAAGM,SAAS,CAAC,eAAe,CAAAK,QAAA,CAAC,mBAAiB,CAAG,CAAC,CAC/C,CAAC,CAEV,CAEA,KAAM,CAAAC,iBAAiB,CAAIP,IAAI,CAACG,OAAO,CAAGD,KAAK,CAAI,GAAG,CACtD,KAAM,CAAAM,iBAAiB,CAAIR,IAAI,CAACI,OAAO,CAAGF,KAAK,CAAI,GAAG,CACtD,KAAM,CAAAO,gBAAgB,CAAIT,IAAI,CAACK,MAAM,CAAGH,KAAK,CAAI,GAAG,CAEpD;AACA,KAAM,CAAAQ,MAAM,CAAG,EAAE,CACjB,KAAM,CAAAC,WAAW,CAAG,EAAE,CACtB,KAAM,CAAAC,gBAAgB,CAAGF,MAAM,CAAGC,WAAW,CAAG,GAAG,CACnD,KAAM,CAAAE,aAAa,CAAGD,gBAAgB,CAAG,CAAC,CAAGE,IAAI,CAACC,EAAE,CAEpD,KAAM,CAAAC,sBAAsB,CAAG,GAAIT,iBAAiB,CAAG,GAAG,CAAIM,aAAa,IAAIA,aAAa,EAAE,CAC9F,KAAM,CAAAI,sBAAsB,CAAG,GAAIT,iBAAiB,CAAG,GAAG,CAAIK,aAAa,IAAIA,aAAa,EAAE,CAC9F,KAAM,CAAAK,qBAAqB,CAAG,GAAIT,gBAAgB,CAAG,GAAG,CAAII,aAAa,IAAIA,aAAa,EAAE,CAE5F,KAAM,CAAAM,aAAa,CAAG,CAAC,CACvB,KAAM,CAAAC,aAAa,CAAG,EAAEb,iBAAiB,CAAG,GAAG,CAAC,CAAGM,aAAa,CAChE,KAAM,CAAAQ,YAAY,CAAG,EAAE,CAACd,iBAAiB,CAAGC,iBAAiB,EAAI,GAAG,CAAC,CAAGK,aAAa,CAErF,mBACEhB,KAAA,QAAKI,SAAS,CAAE,8BAA8BA,SAAS,EAAG,CAAAK,QAAA,eACxDT,KAAA,QAAKI,SAAS,CAAC,UAAU,CAAAK,QAAA,eACvBT,KAAA,QACEyB,MAAM,CAAEZ,MAAM,CAAG,CAAE,CACnBa,KAAK,CAAEb,MAAM,CAAG,CAAE,CAClBT,SAAS,CAAC,sBAAsB,CAAAK,QAAA,eAGhCX,IAAA,WACE6B,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAC,aAAa,CAClBd,WAAW,CAAEA,WAAY,CACzBe,CAAC,CAAEd,gBAAiB,CACpBe,EAAE,CAAEjB,MAAO,CACXkB,EAAE,CAAElB,MAAO,CACZ,CAAC,CAGDV,IAAI,CAACG,OAAO,CAAG,CAAC,eACfR,IAAA,WACE6B,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAC,aAAa,CAClBd,WAAW,CAAEA,WAAY,CACzBkB,eAAe,CAAEb,sBAAuB,CACxCc,gBAAgB,CAAEX,aAAc,CAChCY,aAAa,CAAC,OAAO,CACrBL,CAAC,CAAEd,gBAAiB,CACpBe,EAAE,CAAEjB,MAAO,CACXkB,EAAE,CAAElB,MAAO,CACXT,SAAS,CAAC,6BAA6B,CACxC,CACF,CAGAD,IAAI,CAACI,OAAO,CAAG,CAAC,eACfT,IAAA,WACE6B,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAC,aAAa,CAClBd,WAAW,CAAEA,WAAY,CACzBkB,eAAe,CAAEZ,sBAAuB,CACxCa,gBAAgB,CAAEV,aAAc,CAChCW,aAAa,CAAC,OAAO,CACrBL,CAAC,CAAEd,gBAAiB,CACpBe,EAAE,CAAEjB,MAAO,CACXkB,EAAE,CAAElB,MAAO,CACXT,SAAS,CAAC,6BAA6B,CACxC,CACF,CAGAD,IAAI,CAACK,MAAM,CAAG,CAAC,eACdV,IAAA,WACE6B,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAC,aAAa,CAClBd,WAAW,CAAEA,WAAY,CACzBkB,eAAe,CAAEX,qBAAsB,CACvCY,gBAAgB,CAAET,YAAa,CAC/BU,aAAa,CAAC,OAAO,CACrBL,CAAC,CAAEd,gBAAiB,CACpBe,EAAE,CAAEjB,MAAO,CACXkB,EAAE,CAAElB,MAAO,CACXT,SAAS,CAAC,6BAA6B,CACxC,CACF,EACE,CAAC,cAGNJ,KAAA,QAAKI,SAAS,CAAC,4DAA4D,CAAAK,QAAA,eACzEX,IAAA,SAAMM,SAAS,CAAC,kCAAkC,CAAAK,QAAA,CAAEJ,KAAK,CAAO,CAAC,cACjEP,IAAA,SAAMM,SAAS,CAAC,uBAAuB,CAAAK,QAAA,CAAC,OAAK,CAAM,CAAC,EACjD,CAAC,EACH,CAAC,cAGNT,KAAA,QAAKI,SAAS,CAAC,gBAAgB,CAAAK,QAAA,eAC7BT,KAAA,QAAKI,SAAS,CAAC,6BAA6B,CAAAK,QAAA,eAC1CX,IAAA,QAAKM,SAAS,CAAC,mCAAmC,CAAM,CAAC,cACzDJ,KAAA,SAAMI,SAAS,CAAC,uBAAuB,CAAAK,QAAA,EAAC,WAAS,CAACC,iBAAiB,CAACyB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,EAAM,CAAC,EACrF,CAAC,cACNnC,KAAA,QAAKI,SAAS,CAAC,6BAA6B,CAAAK,QAAA,eAC1CX,IAAA,QAAKM,SAAS,CAAC,oCAAoC,CAAM,CAAC,cAC1DJ,KAAA,SAAMI,SAAS,CAAC,uBAAuB,CAAAK,QAAA,EAAC,WAAS,CAACE,iBAAiB,CAACwB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,EAAM,CAAC,EACrF,CAAC,cACNnC,KAAA,QAAKI,SAAS,CAAC,6BAA6B,CAAAK,QAAA,eAC1CX,IAAA,QAAKM,SAAS,CAAC,iCAAiC,CAAM,CAAC,cACvDJ,KAAA,SAAMI,SAAS,CAAC,uBAAuB,CAAAK,QAAA,EAAC,UAAQ,CAACG,gBAAgB,CAACuB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,EAAM,CAAC,EACnF,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}