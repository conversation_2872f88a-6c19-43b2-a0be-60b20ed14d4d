import React, { useState, useEffect } from "react";
import Layout from "../Layout/Layout";
import { apiService } from "../../api";
import { LoadingSpinner, ErrorMessage } from "../common";

interface Transaction {
  id: number;
  email: string;
  details: any;
  status: string;
  reason: string | null;
  source: string;
  created_at: string;
}

interface PlatformCount {
  source: string;
  total: number;
}

interface StatusCounts {
  success: number;
  pending: number;
  failed: number;
}

const sourceIcons: Record<string, string> = {
  amazon: "fab fa-amazon",
  ebay: "fab fa-ebay",
  stripe: "fab fa-stripe",
  paypal: "fab fa-paypal",
  shopify: "fab fa-shopify",
  default: "fas fa-store",
};

const sourceColors: Record<string, string> = {
  amazon: "bg-yellow-100 text-yellow-600",
  ebay: "bg-blue-100 text-blue-600",
  stripe: "bg-indigo-100 text-indigo-600",
  paypal: "bg-blue-50 text-blue-800",
  shopify: "bg-green-100 text-green-600",
  default: "bg-indigo-100 text-indigo-600",
};

const Dashboard: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");

  const [sources, setSources] = useState<PlatformCount[]>([]);
  const [selectedSource, setSelectedSource] = useState<string | null>(null);
  const [statusCounts, setStatusCounts] = useState<StatusCounts | null>(null);

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true);
        const data = await apiService.getDetails();
        setTransactions(data);

        const sourceMap: Record<string, number> = {};
        data.forEach((t: Transaction) => {
          sourceMap[t.source] = (sourceMap[t.source] || 0) + 1;
        });
        const sourcesArray = Object.entries(sourceMap).map(([source, total]) => ({ source, total }));
        setSources(sourcesArray);
      } catch (e) {
        setError("Failed to load transaction data");
      } finally {
        setLoading(false);
      }
    };
    fetchTransactions();
  }, []);

  useEffect(() => {
    if (!selectedSource) {
      setStatusCounts(null);
      return;
    }

    const filtered = transactions.filter((t) => t.source === selectedSource);
    const counts: StatusCounts = {
      success: 0,
      pending: 0,
      failed: 0,
    };
    filtered.forEach((t) => {
      const status = t.status.toLowerCase();
      if (status === "success") counts.success += 1;
      else if (status === "pending") counts.pending += 1;
      else if (status === "failed") counts.failed += 1;
    });
    setStatusCounts(counts);
  }, [selectedSource, transactions]);

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" message="Loading dashboard..." />
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <ErrorMessage message={error} />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">Overview of transaction sources and their status</p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {sources.map(({ source, total }) => {
            const lowerSource = source.toLowerCase();
            const isSelected = selectedSource === source;
            return (
              <button
                key={source}
                onClick={() => setSelectedSource(source)}
                className={`group relative bg-white rounded-xl border-2 p-6 text-left transition-all duration-200 hover:shadow-lg ${
                  isSelected
                    ? "border-blue-500 shadow-lg ring-4 ring-blue-100"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                aria-pressed={isSelected}
              >
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-lg ${sourceColors[lowerSource] || sourceColors.default} group-hover:scale-110 transition-transform duration-200`}>
                    <i className={`${sourceIcons[lowerSource] || sourceIcons.default} text-xl`} aria-hidden="true" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 capitalize">{source}</h3>
                    <p className="text-sm text-gray-600">
                      {total} transaction{total !== 1 ? "s" : ""}
                    </p>
                  </div>
                </div>
                {isSelected && (
                  <div className="absolute top-3 right-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  </div>
                )}
              </button>
            );
          })}
        </div>

        {selectedSource && statusCounts && (
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className={`p-2 rounded-lg ${sourceColors[selectedSource.toLowerCase()] || sourceColors.default}`}>
                <i className={`${sourceIcons[selectedSource.toLowerCase()] || sourceIcons.default} text-lg`} />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 capitalize">{selectedSource}</h2>
                <p className="text-gray-600">Transaction status breakdown</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-700 font-medium text-sm uppercase tracking-wide">Success</p>
                    <p className="text-3xl font-bold text-green-900 mt-2">{statusCounts.success}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <i className="fas fa-check-circle text-green-600 text-xl" />
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-700 font-medium text-sm uppercase tracking-wide">Pending</p>
                    <p className="text-3xl font-bold text-yellow-900 mt-2">{statusCounts.pending}</p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                    <i className="fas fa-clock text-yellow-600 text-xl" />
                  </div>
                </div>
              </div>

              <div className="bg-red-50 rounded-lg p-6 border border-red-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-red-700 font-medium text-sm uppercase tracking-wide">Failed</p>
                    <p className="text-3xl font-bold text-red-900 mt-2">{statusCounts.failed}</p>
                  </div>
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                    <i className="fas fa-times-circle text-red-600 text-xl" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Dashboard;
