{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import Layout from\"../Layout/Layout\";import{apiService}from\"../../api\";import{LoadingSpinner,ErrorMessage,StatusChart}from\"../common\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const sourceIcons={amazon:\"fab fa-amazon\",ebay:\"fab fa-ebay\",stripe:\"fab fa-stripe\",paypal:\"fab fa-paypal\",shopify:\"fab fa-shopify\",default:\"fas fa-store\"};const sourceColors={amazon:\"bg-yellow-100 text-yellow-600\",ebay:\"bg-blue-100 text-blue-600\",stripe:\"bg-indigo-100 text-indigo-600\",paypal:\"bg-blue-50 text-blue-800\",shopify:\"bg-green-100 text-green-600\",default:\"bg-indigo-100 text-indigo-600\"};const Dashboard=()=>{const[transactions,setTransactions]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(\"\");const[sources,setSources]=useState([]);const[selectedSource,setSelectedSource]=useState(null);const[statusCounts,setStatusCounts]=useState(null);useEffect(()=>{const fetchTransactions=async()=>{try{setLoading(true);const data=await apiService.getDetails();setTransactions(data);const sourceMap={};data.forEach(t=>{sourceMap[t.source]=(sourceMap[t.source]||0)+1;});const sourcesArray=Object.entries(sourceMap).map(_ref=>{let[source,total]=_ref;return{source,total};});setSources(sourcesArray);}catch(e){setError(\"Failed to load transaction data\");}finally{setLoading(false);}};fetchTransactions();},[]);useEffect(()=>{if(!selectedSource){setStatusCounts(null);return;}const filtered=transactions.filter(t=>t.source===selectedSource);const counts={success:0,pending:0,failed:0};filtered.forEach(t=>{const status=t.status.toLowerCase();if(status===\"success\")counts.success+=1;else if(status===\"pending\")counts.pending+=1;else if(status===\"failed\")counts.failed+=1;});setStatusCounts(counts);},[selectedSource,transactions]);if(loading){return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center items-center h-64\",children:/*#__PURE__*/_jsx(LoadingSpinner,{size:\"lg\",message:\"Loading dashboard...\"})})});}if(error){return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center items-center h-64\",children:/*#__PURE__*/_jsx(ErrorMessage,{message:error})})});}return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-between\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900\",children:\"Dashboard\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mt-1\",children:\"Overview of transaction sources and their status\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",children:sources.map(_ref2=>{let{source,total}=_ref2;const lowerSource=source.toLowerCase();const isSelected=selectedSource===source;return/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setSelectedSource(source),className:`group relative bg-white rounded-xl border-2 p-6 text-left transition-all duration-200 hover:shadow-lg ${isSelected?\"border-blue-500 shadow-lg ring-4 ring-blue-100\":\"border-gray-200 hover:border-gray-300\"}`,\"aria-pressed\":isSelected,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:`p-3 rounded-lg ${sourceColors[lowerSource]||sourceColors.default} group-hover:scale-110 transition-transform duration-200`,children:/*#__PURE__*/_jsx(\"i\",{className:`${sourceIcons[lowerSource]||sourceIcons.default} text-xl`,\"aria-hidden\":\"true\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 capitalize\",children:source}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[total,\" transaction\",total!==1?\"s\":\"\"]})]})]}),isSelected&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-3 right-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-blue-500 rounded-full\"})})]},source);})}),selectedSource&&statusCounts&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl border border-gray-200 shadow-sm p-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 mb-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:`p-2 rounded-lg ${sourceColors[selectedSource.toLowerCase()]||sourceColors.default}`,children:/*#__PURE__*/_jsx(\"i\",{className:`${sourceIcons[selectedSource.toLowerCase()]||sourceIcons.default} text-lg`})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 capitalize\",children:selectedSource}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Transaction status breakdown\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-4\",children:\"Status Overview\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check-circle text-green-600\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-green-900\",children:\"Success\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-green-700\",children:\"Completed transactions\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-green-900\",children:statusCounts.success}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-green-700\",children:[(statusCounts.success/(statusCounts.success+statusCounts.pending+statusCounts.failed)*100).toFixed(1),\"%\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-clock text-yellow-600\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-yellow-900\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-yellow-700\",children:\"In progress transactions\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-yellow-900\",children:statusCounts.pending}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-yellow-700\",children:[(statusCounts.pending/(statusCounts.success+statusCounts.pending+statusCounts.failed)*100).toFixed(1),\"%\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-red-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-times-circle text-red-600\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-red-900\",children:\"Failed\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-700\",children:\"Failed transactions\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-red-900\",children:statusCounts.failed}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-red-700\",children:[(statusCounts.failed/(statusCounts.success+statusCounts.pending+statusCounts.failed)*100).toFixed(1),\"%\"]})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-6\",children:\"Visual Distribution\"}),/*#__PURE__*/_jsx(StatusChart,{data:statusCounts})]})]})]})]})});};export default Dashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "apiService", "LoadingSpinner", "ErrorMessage", "StatusChart", "jsx", "_jsx", "jsxs", "_jsxs", "sourceIcons", "amazon", "ebay", "stripe", "paypal", "shopify", "default", "sourceColors", "Dashboard", "transactions", "setTransactions", "loading", "setLoading", "error", "setError", "sources", "setSources", "selectedSource", "setSelectedSource", "statusCounts", "setStatusCounts", "fetchTransactions", "data", "getDetails", "sourceMap", "for<PERSON>ach", "t", "source", "sourcesArray", "Object", "entries", "map", "_ref", "total", "e", "filtered", "filter", "counts", "success", "pending", "failed", "status", "toLowerCase", "children", "className", "size", "message", "_ref2", "lowerSource", "isSelected", "onClick", "toFixed"], "sources": ["D:/ELGI/src/components/Dashboard/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport Layout from \"../Layout/Layout\";\nimport { apiService } from \"../../api\";\nimport { LoadingSpinner, ErrorMessage, StatusChart } from \"../common\";\n\ninterface Transaction {\n  id: number;\n  email: string;\n  details: any;\n  status: string;\n  reason: string | null;\n  source: string;\n  created_at: string;\n}\n\ninterface PlatformCount {\n  source: string;\n  total: number;\n}\n\ninterface StatusCounts {\n  success: number;\n  pending: number;\n  failed: number;\n}\n\nconst sourceIcons: Record<string, string> = {\n  amazon: \"fab fa-amazon\",\n  ebay: \"fab fa-ebay\",\n  stripe: \"fab fa-stripe\",\n  paypal: \"fab fa-paypal\",\n  shopify: \"fab fa-shopify\",\n  default: \"fas fa-store\",\n};\n\nconst sourceColors: Record<string, string> = {\n  amazon: \"bg-yellow-100 text-yellow-600\",\n  ebay: \"bg-blue-100 text-blue-600\",\n  stripe: \"bg-indigo-100 text-indigo-600\",\n  paypal: \"bg-blue-50 text-blue-800\",\n  shopify: \"bg-green-100 text-green-600\",\n  default: \"bg-indigo-100 text-indigo-600\",\n};\n\nconst Dashboard: React.FC = () => {\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string>(\"\");\n\n  const [sources, setSources] = useState<PlatformCount[]>([]);\n  const [selectedSource, setSelectedSource] = useState<string | null>(null);\n  const [statusCounts, setStatusCounts] = useState<StatusCounts | null>(null);\n\n  useEffect(() => {\n    const fetchTransactions = async () => {\n      try {\n        setLoading(true);\n        const data = await apiService.getDetails();\n        setTransactions(data);\n\n        const sourceMap: Record<string, number> = {};\n        data.forEach((t: Transaction) => {\n          sourceMap[t.source] = (sourceMap[t.source] || 0) + 1;\n        });\n        const sourcesArray = Object.entries(sourceMap).map(([source, total]) => ({ source, total }));\n        setSources(sourcesArray);\n      } catch (e) {\n        setError(\"Failed to load transaction data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchTransactions();\n  }, []);\n\n  useEffect(() => {\n    if (!selectedSource) {\n      setStatusCounts(null);\n      return;\n    }\n\n    const filtered = transactions.filter((t) => t.source === selectedSource);\n    const counts: StatusCounts = {\n      success: 0,\n      pending: 0,\n      failed: 0,\n    };\n    filtered.forEach((t) => {\n      const status = t.status.toLowerCase();\n      if (status === \"success\") counts.success += 1;\n      else if (status === \"pending\") counts.pending += 1;\n      else if (status === \"failed\") counts.failed += 1;\n    });\n    setStatusCounts(counts);\n  }, [selectedSource, transactions]);\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"flex justify-center items-center h-64\">\n          <LoadingSpinner size=\"lg\" message=\"Loading dashboard...\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  if (error) {\n    return (\n      <Layout>\n        <div className=\"flex justify-center items-center h-64\">\n          <ErrorMessage message={error} />\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"space-y-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n            <p className=\"text-gray-600 mt-1\">Overview of transaction sources and their status</p>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {sources.map(({ source, total }) => {\n            const lowerSource = source.toLowerCase();\n            const isSelected = selectedSource === source;\n            return (\n              <button\n                key={source}\n                onClick={() => setSelectedSource(source)}\n                className={`group relative bg-white rounded-xl border-2 p-6 text-left transition-all duration-200 hover:shadow-lg ${\n                  isSelected\n                    ? \"border-blue-500 shadow-lg ring-4 ring-blue-100\"\n                    : \"border-gray-200 hover:border-gray-300\"\n                }`}\n                aria-pressed={isSelected}\n              >\n                <div className=\"flex items-center space-x-4\">\n                  <div className={`p-3 rounded-lg ${sourceColors[lowerSource] || sourceColors.default} group-hover:scale-110 transition-transform duration-200`}>\n                    <i className={`${sourceIcons[lowerSource] || sourceIcons.default} text-xl`} aria-hidden=\"true\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 capitalize\">{source}</h3>\n                    <p className=\"text-sm text-gray-600\">\n                      {total} transaction{total !== 1 ? \"s\" : \"\"}\n                    </p>\n                  </div>\n                </div>\n                {isSelected && (\n                  <div className=\"absolute top-3 right-3\">\n                    <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\n                  </div>\n                )}\n              </button>\n            );\n          })}\n        </div>\n\n        {selectedSource && statusCounts && (\n          <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-8\">\n            <div className=\"flex items-center space-x-3 mb-8\">\n              <div className={`p-2 rounded-lg ${sourceColors[selectedSource.toLowerCase()] || sourceColors.default}`}>\n                <i className={`${sourceIcons[selectedSource.toLowerCase()] || sourceIcons.default} text-lg`} />\n              </div>\n              <div>\n                <h2 className=\"text-2xl font-bold text-gray-900 capitalize\">{selectedSource}</h2>\n                <p className=\"text-gray-600\">Transaction status breakdown</p>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              {/* Left Column - Status List */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Status Overview</h3>\n\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n                        <i className=\"fas fa-check-circle text-green-600\" />\n                      </div>\n                      <div>\n                        <p className=\"font-semibold text-green-900\">Success</p>\n                        <p className=\"text-sm text-green-700\">Completed transactions</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-2xl font-bold text-green-900\">{statusCounts.success}</p>\n                      <p className=\"text-sm text-green-700\">\n                        {((statusCounts.success / (statusCounts.success + statusCounts.pending + statusCounts.failed)) * 100).toFixed(1)}%\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center\">\n                        <i className=\"fas fa-clock text-yellow-600\" />\n                      </div>\n                      <div>\n                        <p className=\"font-semibold text-yellow-900\">Pending</p>\n                        <p className=\"text-sm text-yellow-700\">In progress transactions</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-2xl font-bold text-yellow-900\">{statusCounts.pending}</p>\n                      <p className=\"text-sm text-yellow-700\">\n                        {((statusCounts.pending / (statusCounts.success + statusCounts.pending + statusCounts.failed)) * 100).toFixed(1)}%\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-10 h-10 bg-red-100 rounded-full flex items-center justify-center\">\n                        <i className=\"fas fa-times-circle text-red-600\" />\n                      </div>\n                      <div>\n                        <p className=\"font-semibold text-red-900\">Failed</p>\n                        <p className=\"text-sm text-red-700\">Failed transactions</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-2xl font-bold text-red-900\">{statusCounts.failed}</p>\n                      <p className=\"text-sm text-red-700\">\n                        {((statusCounts.failed / (statusCounts.success + statusCounts.pending + statusCounts.failed)) * 100).toFixed(1)}%\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Right Column - Visual Chart */}\n              <div className=\"flex flex-col items-center justify-center\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">Visual Distribution</h3>\n                <StatusChart data={statusCounts} />\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,kBAAkB,CACrC,OAASC,UAAU,KAAQ,WAAW,CACtC,OAASC,cAAc,CAAEC,YAAY,CAAEC,WAAW,KAAQ,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAuBtE,KAAM,CAAAC,WAAmC,CAAG,CAC1CC,MAAM,CAAE,eAAe,CACvBC,IAAI,CAAE,aAAa,CACnBC,MAAM,CAAE,eAAe,CACvBC,MAAM,CAAE,eAAe,CACvBC,OAAO,CAAE,gBAAgB,CACzBC,OAAO,CAAE,cACX,CAAC,CAED,KAAM,CAAAC,YAAoC,CAAG,CAC3CN,MAAM,CAAE,+BAA+B,CACvCC,IAAI,CAAE,2BAA2B,CACjCC,MAAM,CAAE,+BAA+B,CACvCC,MAAM,CAAE,0BAA0B,CAClCC,OAAO,CAAE,6BAA6B,CACtCC,OAAO,CAAE,+BACX,CAAC,CAED,KAAM,CAAAE,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAU,IAAI,CAAC,CACrD,KAAM,CAACwB,KAAK,CAAEC,QAAQ,CAAC,CAAGzB,QAAQ,CAAS,EAAE,CAAC,CAE9C,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAkB,EAAE,CAAC,CAC3D,KAAM,CAAC4B,cAAc,CAAEC,iBAAiB,CAAC,CAAG7B,QAAQ,CAAgB,IAAI,CAAC,CACzE,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAsB,IAAI,CAAC,CAE3EC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+B,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACFT,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAU,IAAI,CAAG,KAAM,CAAA9B,UAAU,CAAC+B,UAAU,CAAC,CAAC,CAC1Cb,eAAe,CAACY,IAAI,CAAC,CAErB,KAAM,CAAAE,SAAiC,CAAG,CAAC,CAAC,CAC5CF,IAAI,CAACG,OAAO,CAAEC,CAAc,EAAK,CAC/BF,SAAS,CAACE,CAAC,CAACC,MAAM,CAAC,CAAG,CAACH,SAAS,CAACE,CAAC,CAACC,MAAM,CAAC,EAAI,CAAC,EAAI,CAAC,CACtD,CAAC,CAAC,CACF,KAAM,CAAAC,YAAY,CAAGC,MAAM,CAACC,OAAO,CAACN,SAAS,CAAC,CAACO,GAAG,CAACC,IAAA,MAAC,CAACL,MAAM,CAAEM,KAAK,CAAC,CAAAD,IAAA,OAAM,CAAEL,MAAM,CAAEM,KAAM,CAAC,EAAC,CAAC,CAC5FjB,UAAU,CAACY,YAAY,CAAC,CAC1B,CAAE,MAAOM,CAAC,CAAE,CACVpB,QAAQ,CAAC,iCAAiC,CAAC,CAC7C,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CACDS,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN/B,SAAS,CAAC,IAAM,CACd,GAAI,CAAC2B,cAAc,CAAE,CACnBG,eAAe,CAAC,IAAI,CAAC,CACrB,OACF,CAEA,KAAM,CAAAe,QAAQ,CAAG1B,YAAY,CAAC2B,MAAM,CAAEV,CAAC,EAAKA,CAAC,CAACC,MAAM,GAAKV,cAAc,CAAC,CACxE,KAAM,CAAAoB,MAAoB,CAAG,CAC3BC,OAAO,CAAE,CAAC,CACVC,OAAO,CAAE,CAAC,CACVC,MAAM,CAAE,CACV,CAAC,CACDL,QAAQ,CAACV,OAAO,CAAEC,CAAC,EAAK,CACtB,KAAM,CAAAe,MAAM,CAAGf,CAAC,CAACe,MAAM,CAACC,WAAW,CAAC,CAAC,CACrC,GAAID,MAAM,GAAK,SAAS,CAAEJ,MAAM,CAACC,OAAO,EAAI,CAAC,CAAC,IACzC,IAAIG,MAAM,GAAK,SAAS,CAAEJ,MAAM,CAACE,OAAO,EAAI,CAAC,CAAC,IAC9C,IAAIE,MAAM,GAAK,QAAQ,CAAEJ,MAAM,CAACG,MAAM,EAAI,CAAC,CAClD,CAAC,CAAC,CACFpB,eAAe,CAACiB,MAAM,CAAC,CACzB,CAAC,CAAE,CAACpB,cAAc,CAAER,YAAY,CAAC,CAAC,CAElC,GAAIE,OAAO,CAAE,CACX,mBACEd,IAAA,CAACN,MAAM,EAAAoD,QAAA,cACL9C,IAAA,QAAK+C,SAAS,CAAC,uCAAuC,CAAAD,QAAA,cACpD9C,IAAA,CAACJ,cAAc,EAACoD,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,sBAAsB,CAAE,CAAC,CACxD,CAAC,CACA,CAAC,CAEb,CAEA,GAAIjC,KAAK,CAAE,CACT,mBACEhB,IAAA,CAACN,MAAM,EAAAoD,QAAA,cACL9C,IAAA,QAAK+C,SAAS,CAAC,uCAAuC,CAAAD,QAAA,cACpD9C,IAAA,CAACH,YAAY,EAACoD,OAAO,CAAEjC,KAAM,CAAE,CAAC,CAC7B,CAAC,CACA,CAAC,CAEb,CAEA,mBACEhB,IAAA,CAACN,MAAM,EAAAoD,QAAA,cACL5C,KAAA,QAAK6C,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxB9C,IAAA,QAAK+C,SAAS,CAAC,mCAAmC,CAAAD,QAAA,cAChD5C,KAAA,QAAA4C,QAAA,eACE9C,IAAA,OAAI+C,SAAS,CAAC,kCAAkC,CAAAD,QAAA,CAAC,WAAS,CAAI,CAAC,cAC/D9C,IAAA,MAAG+C,SAAS,CAAC,oBAAoB,CAAAD,QAAA,CAAC,kDAAgD,CAAG,CAAC,EACnF,CAAC,CACH,CAAC,cAEN9C,IAAA,QAAK+C,SAAS,CAAC,qEAAqE,CAAAD,QAAA,CACjF5B,OAAO,CAACgB,GAAG,CAACgB,KAAA,EAAuB,IAAtB,CAAEpB,MAAM,CAAEM,KAAM,CAAC,CAAAc,KAAA,CAC7B,KAAM,CAAAC,WAAW,CAAGrB,MAAM,CAACe,WAAW,CAAC,CAAC,CACxC,KAAM,CAAAO,UAAU,CAAGhC,cAAc,GAAKU,MAAM,CAC5C,mBACE5B,KAAA,WAEEmD,OAAO,CAAEA,CAAA,GAAMhC,iBAAiB,CAACS,MAAM,CAAE,CACzCiB,SAAS,CAAE,yGACTK,UAAU,CACN,gDAAgD,CAChD,uCAAuC,EAC1C,CACH,eAAcA,UAAW,CAAAN,QAAA,eAEzB5C,KAAA,QAAK6C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9C,IAAA,QAAK+C,SAAS,CAAE,kBAAkBrC,YAAY,CAACyC,WAAW,CAAC,EAAIzC,YAAY,CAACD,OAAO,0DAA2D,CAAAqC,QAAA,cAC5I9C,IAAA,MAAG+C,SAAS,CAAE,GAAG5C,WAAW,CAACgD,WAAW,CAAC,EAAIhD,WAAW,CAACM,OAAO,UAAW,CAAC,cAAY,MAAM,CAAE,CAAC,CAC9F,CAAC,cACNP,KAAA,QAAK6C,SAAS,CAAC,QAAQ,CAAAD,QAAA,eACrB9C,IAAA,OAAI+C,SAAS,CAAC,gDAAgD,CAAAD,QAAA,CAAEhB,MAAM,CAAK,CAAC,cAC5E5B,KAAA,MAAG6C,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EACjCV,KAAK,CAAC,cAAY,CAACA,KAAK,GAAK,CAAC,CAAG,GAAG,CAAG,EAAE,EACzC,CAAC,EACD,CAAC,EACH,CAAC,CACLgB,UAAU,eACTpD,IAAA,QAAK+C,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cACrC9C,IAAA,QAAK+C,SAAS,CAAC,kCAAkC,CAAM,CAAC,CACrD,CACN,GAxBIjB,MAyBC,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,CAELV,cAAc,EAAIE,YAAY,eAC7BpB,KAAA,QAAK6C,SAAS,CAAC,0DAA0D,CAAAD,QAAA,eACvE5C,KAAA,QAAK6C,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/C9C,IAAA,QAAK+C,SAAS,CAAE,kBAAkBrC,YAAY,CAACU,cAAc,CAACyB,WAAW,CAAC,CAAC,CAAC,EAAInC,YAAY,CAACD,OAAO,EAAG,CAAAqC,QAAA,cACrG9C,IAAA,MAAG+C,SAAS,CAAE,GAAG5C,WAAW,CAACiB,cAAc,CAACyB,WAAW,CAAC,CAAC,CAAC,EAAI1C,WAAW,CAACM,OAAO,UAAW,CAAE,CAAC,CAC5F,CAAC,cACNP,KAAA,QAAA4C,QAAA,eACE9C,IAAA,OAAI+C,SAAS,CAAC,6CAA6C,CAAAD,QAAA,CAAE1B,cAAc,CAAK,CAAC,cACjFpB,IAAA,MAAG+C,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,8BAA4B,CAAG,CAAC,EAC1D,CAAC,EACH,CAAC,cAEN5C,KAAA,QAAK6C,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eAEpD5C,KAAA,QAAK6C,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxB9C,IAAA,OAAI+C,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,cAE7E5C,KAAA,QAAK6C,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxB5C,KAAA,QAAK6C,SAAS,CAAC,sFAAsF,CAAAD,QAAA,eACnG5C,KAAA,QAAK6C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9C,IAAA,QAAK+C,SAAS,CAAC,sEAAsE,CAAAD,QAAA,cACnF9C,IAAA,MAAG+C,SAAS,CAAC,oCAAoC,CAAE,CAAC,CACjD,CAAC,cACN7C,KAAA,QAAA4C,QAAA,eACE9C,IAAA,MAAG+C,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,SAAO,CAAG,CAAC,cACvD9C,IAAA,MAAG+C,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,wBAAsB,CAAG,CAAC,EAC7D,CAAC,EACH,CAAC,cACN5C,KAAA,QAAK6C,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9C,IAAA,MAAG+C,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAExB,YAAY,CAACmB,OAAO,CAAI,CAAC,cAC3EvC,KAAA,MAAG6C,SAAS,CAAC,wBAAwB,CAAAD,QAAA,EAClC,CAAExB,YAAY,CAACmB,OAAO,EAAInB,YAAY,CAACmB,OAAO,CAAGnB,YAAY,CAACoB,OAAO,CAAGpB,YAAY,CAACqB,MAAM,CAAC,CAAI,GAAG,EAAEW,OAAO,CAAC,CAAC,CAAC,CAAC,GACnH,EAAG,CAAC,EACD,CAAC,EACH,CAAC,cAENpD,KAAA,QAAK6C,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eACrG5C,KAAA,QAAK6C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9C,IAAA,QAAK+C,SAAS,CAAC,uEAAuE,CAAAD,QAAA,cACpF9C,IAAA,MAAG+C,SAAS,CAAC,8BAA8B,CAAE,CAAC,CAC3C,CAAC,cACN7C,KAAA,QAAA4C,QAAA,eACE9C,IAAA,MAAG+C,SAAS,CAAC,+BAA+B,CAAAD,QAAA,CAAC,SAAO,CAAG,CAAC,cACxD9C,IAAA,MAAG+C,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CAAC,0BAAwB,CAAG,CAAC,EAChE,CAAC,EACH,CAAC,cACN5C,KAAA,QAAK6C,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9C,IAAA,MAAG+C,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAExB,YAAY,CAACoB,OAAO,CAAI,CAAC,cAC5ExC,KAAA,MAAG6C,SAAS,CAAC,yBAAyB,CAAAD,QAAA,EACnC,CAAExB,YAAY,CAACoB,OAAO,EAAIpB,YAAY,CAACmB,OAAO,CAAGnB,YAAY,CAACoB,OAAO,CAAGpB,YAAY,CAACqB,MAAM,CAAC,CAAI,GAAG,EAAEW,OAAO,CAAC,CAAC,CAAC,CAAC,GACnH,EAAG,CAAC,EACD,CAAC,EACH,CAAC,cAENpD,KAAA,QAAK6C,SAAS,CAAC,kFAAkF,CAAAD,QAAA,eAC/F5C,KAAA,QAAK6C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9C,IAAA,QAAK+C,SAAS,CAAC,oEAAoE,CAAAD,QAAA,cACjF9C,IAAA,MAAG+C,SAAS,CAAC,kCAAkC,CAAE,CAAC,CAC/C,CAAC,cACN7C,KAAA,QAAA4C,QAAA,eACE9C,IAAA,MAAG+C,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,QAAM,CAAG,CAAC,cACpD9C,IAAA,MAAG+C,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,qBAAmB,CAAG,CAAC,EACxD,CAAC,EACH,CAAC,cACN5C,KAAA,QAAK6C,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9C,IAAA,MAAG+C,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAExB,YAAY,CAACqB,MAAM,CAAI,CAAC,cACxEzC,KAAA,MAAG6C,SAAS,CAAC,sBAAsB,CAAAD,QAAA,EAChC,CAAExB,YAAY,CAACqB,MAAM,EAAIrB,YAAY,CAACmB,OAAO,CAAGnB,YAAY,CAACoB,OAAO,CAAGpB,YAAY,CAACqB,MAAM,CAAC,CAAI,GAAG,EAAEW,OAAO,CAAC,CAAC,CAAC,CAAC,GAClH,EAAG,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNpD,KAAA,QAAK6C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACxD9C,IAAA,OAAI+C,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,qBAAmB,CAAI,CAAC,cACjF9C,IAAA,CAACF,WAAW,EAAC2B,IAAI,CAAEH,YAAa,CAAE,CAAC,EAChC,CAAC,EACH,CAAC,EACH,CACN,EACE,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}