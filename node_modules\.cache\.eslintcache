[{"D:\\ELGI\\src\\index.tsx": "1", "D:\\ELGI\\src\\reportWebVitals.ts": "2", "D:\\ELGI\\src\\App.tsx": "3", "D:\\ELGI\\src\\components\\ProtectedRoute.tsx": "4", "D:\\ELGI\\src\\contexts\\AuthContext.tsx": "5", "D:\\ELGI\\src\\components\\Login\\Login.tsx": "6", "D:\\ELGI\\src\\components\\Dashboard\\Dashboard.tsx": "7", "D:\\ELGI\\src\\components\\AllTransactions\\AllTransactions.tsx": "8", "D:\\ELGI\\src\\api.ts": "9", "D:\\ELGI\\src\\components\\Layout\\Layout.tsx": "10", "D:\\ELGI\\src\\components\\Layout\\Sidebar.tsx": "11", "D:\\ELGI\\src\\components\\Layout\\Header.tsx": "12", "D:\\ELGI\\src\\components\\common\\LoadingSpinner.tsx": "13", "D:\\ELGI\\src\\components\\common\\ErrorMessage.tsx": "14", "D:\\ELGI\\src\\components\\common\\EmptyState.tsx": "15", "D:\\ELGI\\src\\components\\common\\Pagination.tsx": "16", "D:\\ELGI\\src\\components\\common\\index.ts": "17", "D:\\ELGI\\src\\components\\common\\StatusChart.tsx": "18"}, {"size": 533, "mtime": 1754378700265, "results": "19", "hashOfConfig": "20"}, {"size": 425, "mtime": 1754378715303, "results": "21", "hashOfConfig": "20"}, {"size": 1503, "mtime": 1754459994873, "results": "22", "hashOfConfig": "20"}, {"size": 796, "mtime": 1754557165323, "results": "23", "hashOfConfig": "20"}, {"size": 1850, "mtime": 1754378521980, "results": "24", "hashOfConfig": "20"}, {"size": 7947, "mtime": 1754558171055, "results": "25", "hashOfConfig": "20"}, {"size": 9857, "mtime": 1754558068887, "results": "26", "hashOfConfig": "20"}, {"size": 17006, "mtime": 1754558109169, "results": "27", "hashOfConfig": "20"}, {"size": 3866, "mtime": 1754558130860, "results": "28", "hashOfConfig": "20"}, {"size": 497, "mtime": 1754378565430, "results": "29", "hashOfConfig": "20"}, {"size": 1642, "mtime": 1754556653215, "results": "30", "hashOfConfig": "20"}, {"size": 2018, "mtime": 1754379952104, "results": "31", "hashOfConfig": "20"}, {"size": 723, "mtime": 1754556960160, "results": "32", "hashOfConfig": "20"}, {"size": 1047, "mtime": 1754556967596, "results": "33", "hashOfConfig": "20"}, {"size": 1209, "mtime": 1754556975573, "results": "34", "hashOfConfig": "20"}, {"size": 5844, "mtime": 1754556951157, "results": "35", "hashOfConfig": "20"}, {"size": 284, "mtime": 1754558029762, "results": "36", "hashOfConfig": "20"}, {"size": 4490, "mtime": 1754558022162, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qvi1k9", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ELGI\\src\\index.tsx", [], [], "D:\\ELGI\\src\\reportWebVitals.ts", [], [], "D:\\ELGI\\src\\App.tsx", [], [], "D:\\ELGI\\src\\components\\ProtectedRoute.tsx", [], [], "D:\\ELGI\\src\\contexts\\AuthContext.tsx", [], [], "D:\\ELGI\\src\\components\\Login\\Login.tsx", [], [], "D:\\ELGI\\src\\components\\Dashboard\\Dashboard.tsx", [], [], "D:\\ELGI\\src\\components\\AllTransactions\\AllTransactions.tsx", [], [], "D:\\ELGI\\src\\api.ts", [], [], "D:\\ELGI\\src\\components\\Layout\\Layout.tsx", [], [], "D:\\ELGI\\src\\components\\Layout\\Sidebar.tsx", [], [], "D:\\ELGI\\src\\components\\Layout\\Header.tsx", [], [], "D:\\ELGI\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\ELGI\\src\\components\\common\\ErrorMessage.tsx", [], [], "D:\\ELGI\\src\\components\\common\\EmptyState.tsx", [], [], "D:\\ELGI\\src\\components\\common\\Pagination.tsx", [], [], "D:\\ELGI\\src\\components\\common\\index.ts", [], [], "D:\\ELGI\\src\\components\\common\\StatusChart.tsx", [], []]