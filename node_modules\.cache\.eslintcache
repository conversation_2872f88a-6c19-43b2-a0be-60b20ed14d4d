[{"D:\\ELGI\\src\\index.tsx": "1", "D:\\ELGI\\src\\reportWebVitals.ts": "2", "D:\\ELGI\\src\\App.tsx": "3", "D:\\ELGI\\src\\components\\ProtectedRoute.tsx": "4", "D:\\ELGI\\src\\contexts\\AuthContext.tsx": "5", "D:\\ELGI\\src\\components\\Login\\Login.tsx": "6", "D:\\ELGI\\src\\components\\Dashboard\\Dashboard.tsx": "7", "D:\\ELGI\\src\\components\\AllTransactions\\AllTransactions.tsx": "8", "D:\\ELGI\\src\\api.ts": "9", "D:\\ELGI\\src\\components\\Layout\\Layout.tsx": "10", "D:\\ELGI\\src\\components\\Layout\\Sidebar.tsx": "11", "D:\\ELGI\\src\\components\\Layout\\Header.tsx": "12", "D:\\ELGI\\src\\components\\common\\LoadingSpinner.tsx": "13", "D:\\ELGI\\src\\components\\common\\ErrorMessage.tsx": "14", "D:\\ELGI\\src\\components\\common\\EmptyState.tsx": "15", "D:\\ELGI\\src\\components\\common\\Pagination.tsx": "16", "D:\\ELGI\\src\\components\\common\\index.ts": "17"}, {"size": 533, "mtime": 1754378700265, "results": "18", "hashOfConfig": "19"}, {"size": 425, "mtime": 1754378715303, "results": "20", "hashOfConfig": "19"}, {"size": 1503, "mtime": 1754459994873, "results": "21", "hashOfConfig": "19"}, {"size": 796, "mtime": 1754557165323, "results": "22", "hashOfConfig": "19"}, {"size": 1850, "mtime": 1754378521980, "results": "23", "hashOfConfig": "19"}, {"size": 7621, "mtime": 1754557420945, "results": "24", "hashOfConfig": "19"}, {"size": 8022, "mtime": 1754557155196, "results": "25", "hashOfConfig": "19"}, {"size": 16889, "mtime": 1754557142909, "results": "26", "hashOfConfig": "19"}, {"size": 3436, "mtime": 1754556509575, "results": "27", "hashOfConfig": "19"}, {"size": 497, "mtime": 1754378565430, "results": "28", "hashOfConfig": "19"}, {"size": 1642, "mtime": 1754556653215, "results": "29", "hashOfConfig": "19"}, {"size": 2018, "mtime": 1754379952104, "results": "30", "hashOfConfig": "19"}, {"size": 723, "mtime": 1754556960160, "results": "31", "hashOfConfig": "19"}, {"size": 1047, "mtime": 1754556967596, "results": "32", "hashOfConfig": "19"}, {"size": 1209, "mtime": 1754556975573, "results": "33", "hashOfConfig": "19"}, {"size": 5844, "mtime": 1754556951157, "results": "34", "hashOfConfig": "19"}, {"size": 228, "mtime": 1754557134583, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qvi1k9", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ELGI\\src\\index.tsx", [], [], "D:\\ELGI\\src\\reportWebVitals.ts", [], [], "D:\\ELGI\\src\\App.tsx", [], [], "D:\\ELGI\\src\\components\\ProtectedRoute.tsx", [], [], "D:\\ELGI\\src\\contexts\\AuthContext.tsx", [], [], "D:\\ELGI\\src\\components\\Login\\Login.tsx", [], [], "D:\\ELGI\\src\\components\\Dashboard\\Dashboard.tsx", [], [], "D:\\ELGI\\src\\components\\AllTransactions\\AllTransactions.tsx", [], [], "D:\\ELGI\\src\\api.ts", [], [], "D:\\ELGI\\src\\components\\Layout\\Layout.tsx", [], [], "D:\\ELGI\\src\\components\\Layout\\Sidebar.tsx", [], [], "D:\\ELGI\\src\\components\\Layout\\Header.tsx", [], [], "D:\\ELGI\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\ELGI\\src\\components\\common\\ErrorMessage.tsx", [], [], "D:\\ELGI\\src\\components\\common\\EmptyState.tsx", [], [], "D:\\ELGI\\src\\components\\common\\Pagination.tsx", [], [], "D:\\ELGI\\src\\components\\common\\index.ts", [], []]