[{"D:\\ELGI\\src\\index.tsx": "1", "D:\\ELGI\\src\\reportWebVitals.ts": "2", "D:\\ELGI\\src\\App.tsx": "3", "D:\\ELGI\\src\\components\\ProtectedRoute.tsx": "4", "D:\\ELGI\\src\\contexts\\AuthContext.tsx": "5", "D:\\ELGI\\src\\components\\Login\\Login.tsx": "6", "D:\\ELGI\\src\\components\\Dashboard\\Dashboard.tsx": "7", "D:\\ELGI\\src\\components\\AllTransactions\\AllTransactions.tsx": "8", "D:\\ELGI\\src\\api.ts": "9", "D:\\ELGI\\src\\components\\Layout\\Layout.tsx": "10", "D:\\ELGI\\src\\components\\Layout\\Sidebar.tsx": "11", "D:\\ELGI\\src\\components\\Layout\\Header.tsx": "12", "D:\\ELGI\\src\\components\\common\\LoadingSpinner.tsx": "13", "D:\\ELGI\\src\\components\\common\\ErrorMessage.tsx": "14", "D:\\ELGI\\src\\components\\common\\EmptyState.tsx": "15", "D:\\ELGI\\src\\components\\common\\Pagination.tsx": "16", "D:\\ELGI\\src\\components\\common\\index.ts": "17", "D:\\ELGI\\src\\components\\common\\StatusChart.tsx": "18", "D:\\ELGI\\src\\components\\common\\ELGiLogo.tsx": "19"}, {"size": 533, "mtime": 1754378700265, "results": "20", "hashOfConfig": "21"}, {"size": 425, "mtime": 1754378715303, "results": "22", "hashOfConfig": "21"}, {"size": 1503, "mtime": 1754459994873, "results": "23", "hashOfConfig": "21"}, {"size": 796, "mtime": 1754557165323, "results": "24", "hashOfConfig": "21"}, {"size": 1850, "mtime": 1754378521980, "results": "25", "hashOfConfig": "21"}, {"size": 7891, "mtime": 1754559916864, "results": "26", "hashOfConfig": "21"}, {"size": 9857, "mtime": 1754558068887, "results": "27", "hashOfConfig": "21"}, {"size": 23296, "mtime": 1754558293750, "results": "28", "hashOfConfig": "21"}, {"size": 3866, "mtime": 1754558130860, "results": "29", "hashOfConfig": "21"}, {"size": 497, "mtime": 1754378565430, "results": "30", "hashOfConfig": "21"}, {"size": 1527, "mtime": 1754559935293, "results": "31", "hashOfConfig": "21"}, {"size": 2018, "mtime": 1754379952104, "results": "32", "hashOfConfig": "21"}, {"size": 723, "mtime": 1754556960160, "results": "33", "hashOfConfig": "21"}, {"size": 1047, "mtime": 1754556967596, "results": "34", "hashOfConfig": "21"}, {"size": 1209, "mtime": 1754556975573, "results": "35", "hashOfConfig": "21"}, {"size": 5844, "mtime": 1754556951157, "results": "36", "hashOfConfig": "21"}, {"size": 334, "mtime": 1754559896229, "results": "37", "hashOfConfig": "21"}, {"size": 4490, "mtime": 1754558022162, "results": "38", "hashOfConfig": "21"}, {"size": 1643, "mtime": 1754559888949, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qvi1k9", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ELGI\\src\\index.tsx", [], [], "D:\\ELGI\\src\\reportWebVitals.ts", [], [], "D:\\ELGI\\src\\App.tsx", [], [], "D:\\ELGI\\src\\components\\ProtectedRoute.tsx", [], [], "D:\\ELGI\\src\\contexts\\AuthContext.tsx", [], [], "D:\\ELGI\\src\\components\\Login\\Login.tsx", [], [], "D:\\ELGI\\src\\components\\Dashboard\\Dashboard.tsx", [], [], "D:\\ELGI\\src\\components\\AllTransactions\\AllTransactions.tsx", [], [], "D:\\ELGI\\src\\api.ts", [], [], "D:\\ELGI\\src\\components\\Layout\\Layout.tsx", [], [], "D:\\ELGI\\src\\components\\Layout\\Sidebar.tsx", [], [], "D:\\ELGI\\src\\components\\Layout\\Header.tsx", [], [], "D:\\ELGI\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\ELGI\\src\\components\\common\\ErrorMessage.tsx", [], [], "D:\\ELGI\\src\\components\\common\\EmptyState.tsx", [], [], "D:\\ELGI\\src\\components\\common\\Pagination.tsx", [], [], "D:\\ELGI\\src\\components\\common\\index.ts", [], [], "D:\\ELGI\\src\\components\\common\\StatusChart.tsx", [], [], "D:\\ELGI\\src\\components\\common\\ELGiLogo.tsx", [], []]