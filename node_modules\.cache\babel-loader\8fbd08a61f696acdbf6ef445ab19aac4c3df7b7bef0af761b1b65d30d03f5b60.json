{"ast": null, "code": "var _jsxFileName = \"D:\\\\ELGI\\\\src\\\\components\\\\common\\\\ELGiLogo.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ELGiLogo = ({\n  className = '',\n  width = 120,\n  height = 40\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"svg\", {\n    width: width,\n    height: height,\n    viewBox: \"0 0 120 40\",\n    className: className,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n      width: \"120\",\n      height: \"40\",\n      fill: \"#000000\",\n      rx: \"2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n      fill: \"#FFFFFF\",\n      children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"8\",\n        y: \"8\",\n        width: \"12\",\n        height: \"3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"8\",\n        y: \"8\",\n        width: \"3\",\n        height: \"24\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"8\",\n        y: \"18\",\n        width: \"10\",\n        height: \"3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"8\",\n        y: \"29\",\n        width: \"12\",\n        height: \"3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"25\",\n        y: \"8\",\n        width: \"3\",\n        height: \"24\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"25\",\n        y: \"29\",\n        width: \"12\",\n        height: \"3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"42\",\n        y: \"8\",\n        width: \"12\",\n        height: \"3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"42\",\n        y: \"8\",\n        width: \"3\",\n        height: \"24\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"42\",\n        y: \"29\",\n        width: \"12\",\n        height: \"3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"51\",\n        y: \"18\",\n        width: \"3\",\n        height: \"14\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"48\",\n        y: \"18\",\n        width: \"6\",\n        height: \"3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"59\",\n        y: \"8\",\n        width: \"3\",\n        height: \"3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"59\",\n        y: \"14\",\n        width: \"3\",\n        height: \"18\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n      x: \"66\",\n      y: \"8\",\n      width: \"6\",\n      height: \"6\",\n      fill: \"#DC2626\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n      fill: \"#FFFFFF\",\n      fontSize: \"8\",\n      fontFamily: \"Arial, sans-serif\",\n      children: /*#__PURE__*/_jsxDEV(\"text\", {\n        x: \"75\",\n        y: \"12\",\n        children: \"\\xAE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = ELGiLogo;\nexport default ELGiLogo;\nvar _c;\n$RefreshReg$(_c, \"ELGiLogo\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ELGiLogo", "className", "width", "height", "viewBox", "xmlns", "children", "fill", "rx", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "y", "fontSize", "fontFamily", "_c", "$RefreshReg$"], "sources": ["D:/ELGI/src/components/common/ELGiLogo.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ELGiLogoProps {\n  className?: string;\n  width?: number;\n  height?: number;\n}\n\nconst ELGiLogo: React.FC<ELGiLogoProps> = ({ \n  className = '', \n  width = 120, \n  height = 40 \n}) => {\n  return (\n    <svg\n      width={width}\n      height={height}\n      viewBox=\"0 0 120 40\"\n      className={className}\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      {/* Black background */}\n      <rect width=\"120\" height=\"40\" fill=\"#000000\" rx=\"2\" />\n      \n      {/* ELGi text in white */}\n      <g fill=\"#FFFFFF\">\n        {/* E */}\n        <rect x=\"8\" y=\"8\" width=\"12\" height=\"3\" />\n        <rect x=\"8\" y=\"8\" width=\"3\" height=\"24\" />\n        <rect x=\"8\" y=\"18\" width=\"10\" height=\"3\" />\n        <rect x=\"8\" y=\"29\" width=\"12\" height=\"3\" />\n        \n        {/* L */}\n        <rect x=\"25\" y=\"8\" width=\"3\" height=\"24\" />\n        <rect x=\"25\" y=\"29\" width=\"12\" height=\"3\" />\n        \n        {/* G */}\n        <rect x=\"42\" y=\"8\" width=\"12\" height=\"3\" />\n        <rect x=\"42\" y=\"8\" width=\"3\" height=\"24\" />\n        <rect x=\"42\" y=\"29\" width=\"12\" height=\"3\" />\n        <rect x=\"51\" y=\"18\" width=\"3\" height=\"14\" />\n        <rect x=\"48\" y=\"18\" width=\"6\" height=\"3\" />\n        \n        {/* i */}\n        <rect x=\"59\" y=\"8\" width=\"3\" height=\"3\" />\n        <rect x=\"59\" y=\"14\" width=\"3\" height=\"18\" />\n      </g>\n      \n      {/* Red square accent */}\n      <rect x=\"66\" y=\"8\" width=\"6\" height=\"6\" fill=\"#DC2626\" />\n      \n      {/* Registered trademark symbol */}\n      <g fill=\"#FFFFFF\" fontSize=\"8\" fontFamily=\"Arial, sans-serif\">\n        <text x=\"75\" y=\"12\">®</text>\n      </g>\n    </svg>\n  );\n};\n\nexport default ELGiLogo;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ1B,MAAMC,QAAiC,GAAGA,CAAC;EACzCC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG;AACX,CAAC,KAAK;EACJ,oBACEJ,OAAA;IACEG,KAAK,EAAEA,KAAM;IACbC,MAAM,EAAEA,MAAO;IACfC,OAAO,EAAC,YAAY;IACpBH,SAAS,EAAEA,SAAU;IACrBI,KAAK,EAAC,4BAA4B;IAAAC,QAAA,gBAGlCP,OAAA;MAAMG,KAAK,EAAC,KAAK;MAACC,MAAM,EAAC,IAAI;MAACI,IAAI,EAAC,SAAS;MAACC,EAAE,EAAC;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtDb,OAAA;MAAGQ,IAAI,EAAC,SAAS;MAAAD,QAAA,gBAEfP,OAAA;QAAMc,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,GAAG;QAACZ,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC;MAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1Cb,OAAA;QAAMc,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,GAAG;QAACZ,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1Cb,OAAA;QAAMc,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,IAAI;QAACZ,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC;MAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cb,OAAA;QAAMc,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,IAAI;QAACZ,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC;MAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG3Cb,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAACZ,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cb,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACZ,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC;MAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG5Cb,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAACZ,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC;MAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cb,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAACZ,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cb,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACZ,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC;MAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5Cb,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACZ,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5Cb,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACZ,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG3Cb,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAACZ,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1Cb,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACZ,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eAGJb,OAAA;MAAMc,CAAC,EAAC,IAAI;MAACC,CAAC,EAAC,GAAG;MAACZ,KAAK,EAAC,GAAG;MAACC,MAAM,EAAC,GAAG;MAACI,IAAI,EAAC;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGzDb,OAAA;MAAGQ,IAAI,EAAC,SAAS;MAACQ,QAAQ,EAAC,GAAG;MAACC,UAAU,EAAC,mBAAmB;MAAAV,QAAA,eAC3DP,OAAA;QAAMc,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAAAR,QAAA,EAAC;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACK,EAAA,GAjDIjB,QAAiC;AAmDvC,eAAeA,QAAQ;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}